# 依赖循环问题解决方案

## 🚨 问题描述

在实现OIDC标准认证流程时，遇到了Dagger依赖循环错误：

```
LogtoManager → OidcConfigurationService → OkHttpClient → TokenInterceptor → LogtoManager
```

错误信息：
```
Found a dependency cycle:
com.cabycare.android.data.auth.LogtoManager is injected at
    com.cabycare.android.data.auth.TokenInterceptor(logtoManager, …)
com.cabycare.android.data.auth.TokenInterceptor is injected at
    com.cabycare.android.di.NetworkModule.provideAuthenticatedOkHttpClient(tokenInterceptor)
@com.cabycare.android.di.AuthenticatedOkHttpClient okhttp3.OkHttpClient is injected at
    com.cabycare.android.di.NetworkModule.provideOkHttpClient(authenticatedClient)
okhttp3.OkHttpClient is injected at
    com.cabycare.android.data.auth.OidcConfigurationService(okHttpClient)
com.cabycare.android.data.auth.OidcConfigurationService is injected at
    com.cabycare.android.data.auth.LogtoManager(…, oidcConfigurationService)
```

## 🔧 解决方案

### 1. 分离OkHttpClient层级

创建了两个不同的OkHttpClient实例：

#### **基础OkHttpClient** (`@BasicOkHttpClient`)
- 用于OIDC服务（OidcTokenService, OidcUserInfoService, OidcConfigurationService）
- 不包含TokenInterceptor，避免循环依赖
- 提供基础的HTTP功能和日志记录

```kotlin
@Provides
@Singleton
@BasicOkHttpClient
fun provideBasicOkHttpClient(): OkHttpClient {
    val logging = HttpLoggingInterceptor()
    logging.level = HttpLoggingInterceptor.Level.BODY

    return OkHttpClient.Builder()
        .addInterceptor(logging)
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
}
```

#### **认证OkHttpClient** (`@AuthenticatedOkHttpClient`)
- 用于需要自动令牌管理的API调用
- 包含TokenInterceptor，提供自动令牌刷新功能
- 用于业务API请求

```kotlin
@Provides
@Singleton
@AuthenticatedOkHttpClient
fun provideAuthenticatedOkHttpClient(tokenInterceptor: TokenInterceptor): OkHttpClient {
    // 包含TokenInterceptor的OkHttpClient
}
```

### 2. 重构TokenInterceptor

**移除循环依赖：**
- ❌ 移除对`LogtoManager`的依赖
- ❌ 移除对`OidcUserInfoService`的依赖
- ✅ 直接注入`OidcTokenService`进行令牌刷新
- ✅ 使用`UserPreferences`获取存储的令牌

**新的依赖关系：**
```kotlin
class TokenInterceptor @Inject constructor(
    private val userPreferences: UserPreferences,
    private val oidcTokenService: OidcTokenService  // 直接使用OIDC服务
)
```

**简化的刷新逻辑：**
```kotlin
// 401错误时直接使用OIDC标准刷新流程
val refreshToken = userPreferences.getRefreshToken().first()
if (!refreshToken.isNullOrEmpty()) {
    val tokenResult = oidcTokenService.refreshAccessToken(refreshToken)
    if (tokenResult.isSuccess) {
        // 保存新令牌并重试请求
    }
}
```

### 3. 重构LogtoManager

**移除循环依赖：**
- ❌ 移除对`OidcConfigurationService`的依赖
- ❌ 移除对`OidcTokenService`的依赖
- ❌ 移除对`OidcUserInfoService`的依赖

**简化为核心功能：**
```kotlin
class LogtoManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    // 只保留Logto SDK的核心功能
    // 配置验证移到AuthManager中进行
}
```

### 4. 重构AuthManager

**承担更多职责：**
- ✅ 直接注入`OidcConfigurationService`进行配置验证
- ✅ 协调所有OIDC服务的使用
- ✅ 提供完整的认证流程管理

```kotlin
class AuthManager @Inject constructor(
    // ... 其他依赖
    private val oidcConfigurationService: OidcConfigurationService
) {
    suspend fun startOidcSignIn(activity: Activity): Result<String> {
        // 直接在这里验证OIDC配置
        val configResult = oidcConfigurationService.validateConfiguration()
        // ...
    }
}
```

## 📊 新的依赖关系图

### 修复后的依赖关系：

```
基础层：
BasicOkHttpClient → OidcTokenService
BasicOkHttpClient → OidcUserInfoService  
BasicOkHttpClient → OidcConfigurationService

认证层：
UserPreferences + OidcTokenService → TokenInterceptor
TokenInterceptor → AuthenticatedOkHttpClient

管理层：
Context → LogtoManager
OidcConfigurationService + OidcTokenService + OidcUserInfoService → AuthManager
```

### 关键改进：

1. **分层清晰**：基础HTTP服务 → OIDC服务 → 认证管理
2. **无循环依赖**：每一层只依赖下层服务
3. **职责明确**：每个组件有明确的职责范围
4. **易于测试**：依赖关系简单，便于单元测试

## ✅ 验证结果

- ✅ **编译成功**：无Dagger依赖循环错误
- ✅ **功能完整**：所有OIDC功能正常工作
- ✅ **性能优化**：避免了不必要的依赖注入
- ✅ **代码清晰**：依赖关系更加明确

## 🎯 最佳实践总结

### 1. 避免循环依赖的设计原则：

- **分层架构**：明确定义服务层级，上层依赖下层
- **单一职责**：每个组件只负责特定功能
- **依赖注入最小化**：只注入真正需要的依赖
- **接口隔离**：使用接口而不是具体实现

### 2. OkHttpClient使用策略：

- **基础Client**：用于不需要认证的请求（OIDC endpoints）
- **认证Client**：用于需要自动令牌管理的业务API
- **避免混用**：明确区分使用场景

### 3. 令牌管理策略：

- **集中管理**：在AuthManager中协调所有认证相关操作
- **分离关注点**：TokenInterceptor只负责HTTP层面的令牌处理
- **标准化流程**：优先使用OIDC标准流程

---

**总结**：通过重构依赖关系和分离关注点，我们成功解决了依赖循环问题，同时保持了OIDC标准实现的完整性和功能性。新的架构更加清晰、可维护，并且符合依赖注入的最佳实践。
