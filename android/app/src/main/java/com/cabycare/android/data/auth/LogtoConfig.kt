package com.cabycare.android.data.auth

/**
 * Logto配置常量
 * 基于OpenID Connect Core 1.0规范和Logto官方endpoints
 */
object LogtoConfig {
    // Logto服务器配置
    const val LOGTO_ENDPOINT = "https://login.caby.care"
    const val CLIENT_ID = "d2oz2qj0ppjdpf6de14ha"
    const val REDIRECT_URI = "com.cabycare.android://callback"

    // OIDC标准endpoints（按照OpenID Connect Core 1.0规范）
    const val ISSUER_ENDPOINT = "https://login.caby.care/oidc"
    const val AUTHORIZATION_ENDPOINT = "https://login.caby.care/oidc/auth"
    const val TOKEN_ENDPOINT = "https://login.caby.care/oidc/token"
    const val USERINFO_ENDPOINT = "https://login.caby.care/oidc/me"
    const val JWKS_URI = "https://login.caby.care/oidc/jwks"
    const val OPENID_CONFIGURATION_ENDPOINT = "https://login.caby.care/oidc/.well-known/openid-configuration"

    // API配置（后端自定义endpoints，用于获取user_id等）
    const val API_BASE_URL = "https://api.caby.care"

    // OAuth范围（按照OIDC规范）
    val SCOPES = listOf(
        "openid",      // 必需的OIDC scope
        "profile",     // 用户基本信息
        "email",       // 用户邮箱
        "offline_access" // 获取refresh_token的必需scope
    )

    // 资源
    val RESOURCES = listOf(
        API_BASE_URL
    )

    // PKCE配置
    const val CODE_CHALLENGE_METHOD = "S256"

    // 令牌类型
    const val TOKEN_TYPE = "Bearer"
}
