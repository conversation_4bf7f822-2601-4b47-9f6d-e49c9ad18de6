package com.cabycare.android.data.auth

import android.util.Log
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import java.security.MessageDigest
import java.security.SecureRandom
import java.util.Base64
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * OIDC令牌服务
 * 按照OpenID Connect Core 1.0规范实现标准的Authorization Code Flow with PKCE
 * 
 * 参考文档：
 * - OpenID Connect Core 1.0: https://openid.net/specs/openid-connect-core-1_0.html
 * - RFC 7636 (PKCE): https://tools.ietf.org/html/rfc7636
 * - Logto文档: https://docs.logto.io/
 */
@Singleton
class OidcTokenService @Inject constructor(
    private val okHttpClient: OkHttpClient
) {
    
    companion object {
        private const val TAG = "OidcTokenService"
    }
    
    /**
     * OIDC令牌响应模型
     * 按照OpenID Connect Core 1.0 Section 3.1.3.3定义
     */
    @Serializable
    data class TokenResponse(
        @SerialName("access_token")
        val accessToken: String,
        
        @SerialName("token_type")
        val tokenType: String = "Bearer",
        
        @SerialName("expires_in")
        val expiresIn: Long? = null,
        
        @SerialName("refresh_token")
        val refreshToken: String? = null,
        
        @SerialName("id_token")
        val idToken: String? = null,
        
        @SerialName("scope")
        val scope: String? = null
    )
    
    /**
     * OIDC错误响应模型
     * 按照OpenID Connect Core 1.0 Section *******定义
     */
    @Serializable
    data class TokenErrorResponse(
        @SerialName("error")
        val error: String,
        
        @SerialName("error_description")
        val errorDescription: String? = null,
        
        @SerialName("error_uri")
        val errorUri: String? = null
    )
    
    /**
     * PKCE状态存储
     */
    data class PkceState(
        val codeVerifier: String,
        val codeChallenge: String,
        val state: String
    )
    
    /**
     * 生成PKCE参数
     * 按照RFC 7636规范实现
     */
    fun generatePkceParameters(): PkceState {
        Log.d(TAG, "🔐 生成PKCE参数")
        
        // 生成code_verifier (RFC 7636 Section 4.1)
        val codeVerifier = generateCodeVerifier()
        
        // 生成code_challenge (RFC 7636 Section 4.2)
        val codeChallenge = generateCodeChallenge(codeVerifier)
        
        // 生成state参数用于CSRF保护
        val state = generateState()
        
        Log.d(TAG, "✅ PKCE参数生成完成")
        Log.d(TAG, "  - code_verifier长度: ${codeVerifier.length}")
        Log.d(TAG, "  - code_challenge: ${codeChallenge.take(20)}...")
        Log.d(TAG, "  - state: ${state.take(10)}...")
        
        return PkceState(codeVerifier, codeChallenge, state)
    }
    
    /**
     * 构建授权URL
     * 按照OpenID Connect Core 1.0 Section 3.1.2.1实现
     */
    fun buildAuthorizationUrl(pkceState: PkceState): String {
        Log.d(TAG, "🔗 构建授权URL")
        
        val params = mutableMapOf(
            "client_id" to LogtoConfig.CLIENT_ID,
            "redirect_uri" to LogtoConfig.REDIRECT_URI,
            "response_type" to "code",
            "scope" to LogtoConfig.SCOPES.joinToString(" "),
            "state" to pkceState.state,
            "code_challenge" to pkceState.codeChallenge,
            "code_challenge_method" to LogtoConfig.CODE_CHALLENGE_METHOD,
            "prompt" to "consent"
        )
        
        // 添加资源参数（如果有）
        if (LogtoConfig.RESOURCES.isNotEmpty()) {
            params["resource"] = LogtoConfig.RESOURCES.joinToString(" ")
        }
        
        val queryString = params.map { "${it.key}=${java.net.URLEncoder.encode(it.value, "UTF-8")}" }
            .joinToString("&")
        
        val authUrl = "${LogtoConfig.AUTHORIZATION_ENDPOINT}?$queryString"
        
        Log.d(TAG, "✅ 授权URL构建完成: ${authUrl.take(100)}...")
        return authUrl
    }
    
    /**
     * 使用授权码交换令牌
     * 按照OpenID Connect Core 1.0 Section 3.1.3实现
     */
    suspend fun exchangeCodeForTokens(
        code: String,
        codeVerifier: String,
        state: String
    ): Result<TokenResponse> = suspendCancellableCoroutine { continuation ->
        
        Log.i(TAG, "🔄 使用授权码交换令牌")
        Log.d(TAG, "  - code: ${code.take(20)}...")
        Log.d(TAG, "  - code_verifier: ${codeVerifier.take(20)}...")
        Log.d(TAG, "  - state: ${state.take(10)}...")
        
        try {
            // 构建令牌请求 (OpenID Connect Core 1.0 Section 3.1.3.1)
            val requestBody = FormBody.Builder()
                .add("grant_type", "authorization_code")
                .add("client_id", LogtoConfig.CLIENT_ID)
                .add("code", code)
                .add("redirect_uri", LogtoConfig.REDIRECT_URI)
                .add("code_verifier", codeVerifier)
                .build()
            
            val request = Request.Builder()
                .url(LogtoConfig.TOKEN_ENDPOINT)
                .post(requestBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build()
            
            Log.d(TAG, "📡 发送令牌请求到: ${LogtoConfig.TOKEN_ENDPOINT}")
            
            okHttpClient.newCall(request).enqueue(object : okhttp3.Callback {
                override fun onFailure(call: okhttp3.Call, e: java.io.IOException) {
                    Log.e(TAG, "❌ 令牌请求网络失败: ${e.message}", e)
                    continuation.resume(Result.failure(e))
                }
                
                override fun onResponse(call: okhttp3.Call, response: okhttp3.Response) {
                    try {
                        val responseBody = response.body?.string() ?: ""
                        Log.d(TAG, "📡 令牌响应: 状态码=${response.code}")
                        
                        if (response.isSuccessful) {
                            Log.d(TAG, "📡 令牌响应内容: $responseBody")
                            
                            val tokenResponse = Json.decodeFromString<TokenResponse>(responseBody)
                            
                            Log.i(TAG, "✅ 令牌交换成功")
                            Log.d(TAG, "🔍 令牌详情:")
                            Log.d(TAG, "  - access_token长度: ${tokenResponse.accessToken.length}")
                            Log.d(TAG, "  - token_type: ${tokenResponse.tokenType}")
                            Log.d(TAG, "  - expires_in: ${tokenResponse.expiresIn}")
                            Log.d(TAG, "  - refresh_token存在: ${tokenResponse.refreshToken != null}")
                            Log.d(TAG, "  - id_token存在: ${tokenResponse.idToken != null}")
                            Log.d(TAG, "  - scope: ${tokenResponse.scope}")
                            
                            continuation.resume(Result.success(tokenResponse))
                        } else {
                            Log.e(TAG, "❌ 令牌请求失败: HTTP ${response.code}")
                            Log.e(TAG, "❌ 错误响应: $responseBody")
                            
                            try {
                                val errorResponse = Json.decodeFromString<TokenErrorResponse>(responseBody)
                                val exception = Exception("Token exchange failed: ${errorResponse.error} - ${errorResponse.errorDescription}")
                                continuation.resume(Result.failure(exception))
                            } catch (e: Exception) {
                                val exception = Exception("Token exchange failed: HTTP ${response.code} - $responseBody")
                                continuation.resume(Result.failure(exception))
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 解析令牌响应失败: ${e.message}", e)
                        continuation.resume(Result.failure(e))
                    }
                }
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 构建令牌请求失败: ${e.message}", e)
            continuation.resume(Result.failure(e))
        }
    }
    
    /**
     * 使用刷新令牌获取新的访问令牌
     * 按照OpenID Connect Core 1.0 Section 12实现
     */
    suspend fun refreshAccessToken(refreshToken: String): Result<TokenResponse> = suspendCancellableCoroutine { continuation ->
        
        Log.i(TAG, "🔄 使用刷新令牌获取新的访问令牌")
        Log.d(TAG, "  - refresh_token: ${refreshToken.take(20)}...")
        
        try {
            // 构建刷新令牌请求 (OpenID Connect Core 1.0 Section 12)
            val requestBody = FormBody.Builder()
                .add("grant_type", "refresh_token")
                .add("client_id", LogtoConfig.CLIENT_ID)
                .add("refresh_token", refreshToken)
                .build()
            
            val request = Request.Builder()
                .url(LogtoConfig.TOKEN_ENDPOINT)
                .post(requestBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build()
            
            Log.d(TAG, "📡 发送刷新令牌请求到: ${LogtoConfig.TOKEN_ENDPOINT}")
            
            okHttpClient.newCall(request).enqueue(object : okhttp3.Callback {
                override fun onFailure(call: okhttp3.Call, e: java.io.IOException) {
                    Log.e(TAG, "❌ 刷新令牌请求网络失败: ${e.message}", e)
                    continuation.resume(Result.failure(e))
                }
                
                override fun onResponse(call: okhttp3.Call, response: okhttp3.Response) {
                    try {
                        val responseBody = response.body?.string() ?: ""
                        Log.d(TAG, "📡 刷新令牌响应: 状态码=${response.code}")
                        
                        if (response.isSuccessful) {
                            Log.d(TAG, "📡 刷新令牌响应内容: $responseBody")
                            
                            val tokenResponse = Json.decodeFromString<TokenResponse>(responseBody)
                            
                            Log.i(TAG, "✅ 令牌刷新成功")
                            Log.d(TAG, "🔍 新令牌详情:")
                            Log.d(TAG, "  - access_token长度: ${tokenResponse.accessToken.length}")
                            Log.d(TAG, "  - expires_in: ${tokenResponse.expiresIn}")
                            Log.d(TAG, "  - refresh_token存在: ${tokenResponse.refreshToken != null}")
                            
                            continuation.resume(Result.success(tokenResponse))
                        } else {
                            Log.e(TAG, "❌ 刷新令牌失败: HTTP ${response.code}")
                            Log.e(TAG, "❌ 错误响应: $responseBody")
                            
                            try {
                                val errorResponse = Json.decodeFromString<TokenErrorResponse>(responseBody)
                                val exception = Exception("Token refresh failed: ${errorResponse.error} - ${errorResponse.errorDescription}")
                                continuation.resume(Result.failure(exception))
                            } catch (e: Exception) {
                                val exception = Exception("Token refresh failed: HTTP ${response.code} - $responseBody")
                                continuation.resume(Result.failure(exception))
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 解析刷新令牌响应失败: ${e.message}", e)
                        continuation.resume(Result.failure(e))
                    }
                }
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 构建刷新令牌请求失败: ${e.message}", e)
            continuation.resume(Result.failure(e))
        }
    }
    
    // MARK: - Private Helper Methods
    
    /**
     * 生成code_verifier
     * 按照RFC 7636 Section 4.1实现
     */
    private fun generateCodeVerifier(): String {
        val bytes = ByteArray(32)
        SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }
    
    /**
     * 生成code_challenge
     * 按照RFC 7636 Section 4.2实现，使用S256方法
     */
    private fun generateCodeChallenge(codeVerifier: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(codeVerifier.toByteArray())
        return Base64.getUrlEncoder().withoutPadding().encodeToString(hash)
    }
    
    /**
     * 生成state参数用于CSRF保护
     */
    private fun generateState(): String {
        val bytes = ByteArray(16)
        SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }
}
