package com.cabycare.android.ui.home

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.model.AccessibleDevice
import com.cabycare.android.data.model.CatProfile
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.repository.CatRepository
import com.cabycare.android.data.repository.DeviceRepository
import com.cabycare.android.data.repository.UserRepository
import com.cabycare.android.data.auth.AuthManager
import com.cabycare.android.data.service.CatStatisticsService
import com.cabycare.android.data.local.UserPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import javax.inject.Inject

/**
 * 猫咪警报数据
 */
data class CatAlert(
    val catId: String,
    val catName: String,
    val message: String,
    val severity: AlertSeverity
)

/**
 * 警报严重程度
 */
enum class AlertSeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}

/**
 * 猫咪统计数据（现在使用真实的如厕数据）
 */
data class CatStatistic(
    val catId: String,
    val name: String,
    val age: String,
    val gender: String,
    val healthStatus: String,
    val todayActivity: Int, // 今日如厕次数
    val healthScore: Int, // 基于如厕频率和规律性的健康评分
    val activityLevel: String, // 活跃度等级
    val avatarUrl: String? = null // 猫咪头像URL
)

/**
 * 首页UI状态
 */
data class HomeUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val alertCats: List<CatAlert> = emptyList(),
    val catStats: List<CatStatistic> = emptyList(),
    val isCalculating: Boolean = false
)

/**
 * 首页ViewModel
 * 现在使用真实的猫咪如厕数据而不是假数据
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val catRepository: CatRepository,
    private val deviceRepository: DeviceRepository,
    private val userRepository: UserRepository,
    private val authManager: AuthManager, // 注入AuthManager获取用户信息
    private val catStatisticsService: CatStatisticsService, // 注入统计计算服务
    val userPreferences: UserPreferences, // 提供给UI使用
    val okHttpClient: OkHttpClient // 提供给UI使用
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    companion object {
        private const val TAG = "HomeViewModel"
    }
    
    /**
     * 加载数据 - 使用真实的如厕统计数据
     */
    fun loadData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                // 优先从AuthState获取用户ID
                val authState = authManager.authState.first()
                val userId = authState.user?.userId

                Log.d(TAG, "🔍 HomeViewModel获取用户信息:")
                Log.d(TAG, "  - authState.isAuthenticated: ${authState.isAuthenticated}")
                Log.d(TAG, "  - authState.user: ${authState.user}")
                Log.d(TAG, "  - userId from AuthState: $userId")

                if (userId.isNullOrEmpty()) {
                    // 如果AuthState中没有用户信息，尝试从UserRepository获取
                    Log.w(TAG, "⚠️ AuthState中没有用户信息，尝试从UserRepository获取")
                    val fallbackUserId = userRepository.getUserId().first()
                    Log.d(TAG, "  - fallback userId: $fallbackUserId")

                    if (fallbackUserId.isNullOrEmpty()) {
                        Log.e(TAG, "❌ 无法获取用户ID，用户可能未登录")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "用户未登录或用户信息缺失"
                        )
                        return@launch
                    } else {
                        // 使用fallback的用户ID
                        Log.i(TAG, "✅ 使用fallback用户ID: $fallbackUserId")
                        loadCatsData(fallbackUserId)
                        return@launch
                    }
                }

                Log.i(TAG, "✅ 使用AuthState中的用户ID: $userId")
                loadCatsData(userId)

            } catch (e: Exception) {
                Log.e(TAG, "❌ 加载数据失败: ${e.message}", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载数据失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 加载猫咪数据的具体实现
     */
    private suspend fun loadCatsData(userId: String) {
        try {
            // 获取猫咪列表
            when (val catsResult = catRepository.getCats()) {
                is NetworkResult.Success -> {
                    val cats = catsResult.data

                    if (cats.isEmpty()) {
                        // 如果没有猫咪，显示空状态
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            catStats = emptyList(),
                            alertCats = emptyList(),
                            error = null
                        )
                        return
                    }

                    // 开始计算真实的统计数据
                    _uiState.value = _uiState.value.copy(isCalculating = true)

                    // 使用CatStatisticsService计算真实的统计数据
                    val (catStats, alerts) = catStatisticsService.calculateAllCatStatistics(cats, userId)

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isCalculating = false,
                        catStats = catStats,
                        alertCats = alerts,
                        error = null
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isCalculating = false,
                        error = catsResult.exception.message ?: "加载猫咪数据失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isCalculating = false,
                        error = "未知错误"
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 加载猫咪数据失败: ${e.message}", e)
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                isCalculating = false,
                error = e.message ?: "加载失败"
            )
        }
    }
    
    /**
     * 刷新数据
     */
    fun refreshData() {
        loadData()
    }
}
