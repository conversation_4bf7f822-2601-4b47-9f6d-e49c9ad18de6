package com.cabycare.android.data.auth

import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 令牌刷新管理器
 * 使用Logto SDK内部的令牌管理机制，无需手动管理令牌刷新
 * Logto SDK会自动处理令牌过期和刷新
 */
@Singleton
class TokenRefreshManager @Inject constructor(
    private val logtoManager: LogtoManager
) : DefaultLifecycleObserver {

    companion object {
        private const val TAG = "TokenRefreshManager"
    }

    init {
        // 注册应用生命周期监听
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        Log.i(TAG, "🔄 TokenRefreshManager 初始化完成（使用Logto SDK自动管理）")
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        Log.d(TAG, "📱 应用进入前台，Logto SDK自动管理令牌")
        // Logto SDK会自动处理令牌刷新，无需手动干预
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        Log.d(TAG, "📱 应用进入后台，Logto SDK继续管理令牌")
        // Logto SDK会继续在后台管理令牌，无需手动干预
    }

    /**
     * 应用启动时的令牌检查
     * Logto SDK会自动处理令牌状态检查和刷新
     */
    fun performStartupTokenCheck() {
        Log.d(TAG, "🔍 应用启动 - Logto SDK自动处理令牌状态")
        // Logto SDK会自动处理令牌状态检查和刷新，无需手动干预
    }
}
