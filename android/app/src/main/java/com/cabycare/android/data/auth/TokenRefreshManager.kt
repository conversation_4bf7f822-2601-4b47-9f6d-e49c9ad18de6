package com.cabycare.android.data.auth

import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 令牌刷新管理器
 * 负责应用生命周期中的令牌自动刷新
 * 使用Logto SDK内部的令牌管理机制
 */
@Singleton
class TokenRefreshManager @Inject constructor(
    private val authManager: AuthManager,
    private val logtoTokenManager: LogtoTokenManager
) : DefaultLifecycleObserver {
    
    companion object {
        private const val TAG = "TokenRefreshManager"
        private const val TOKEN_REFRESH_INTERVAL = 10 * 60 * 1000L // 10分钟检查一次
        private const val TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000L // 5分钟内过期就刷新
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var refreshJob: Job? = null
    private var isAutoRefreshing = false
    
    init {
        // 注册应用生命周期监听
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        Log.i(TAG, "🔄 TokenRefreshManager 初始化完成")
    }
    
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        Log.i(TAG, "📱 应用进入前台")
        handleAppBecomeActive()
    }
    
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        Log.i(TAG, "📱 应用进入后台")
        stopPeriodicRefresh()
    }
    
    /**
     * 应用启动时的令牌检查
     */
    fun performStartupTokenCheck() {
        scope.launch {
            Log.i(TAG, "🔍 应用启动 - 开始令牌状态检查")
            
            val authState = authManager.authState.first()
            if (!authState.isAuthenticated) {
                Log.d(TAG, "🔍 用户未认证，跳过令牌检查")
                return@launch
            }
            
            // 检查令牌状态并刷新
            silentlyRefreshTokenIfNeeded()
        }
    }
    
    /**
     * 应用进入前台时的处理
     */
    private fun handleAppBecomeActive() {
        scope.launch {
            Log.i(TAG, "🔍 应用进入前台 - 检查令牌状态")
            
            val authState = authManager.authState.first()
            if (!authState.isAuthenticated) {
                Log.d(TAG, "🔍 用户未认证，跳过令牌检查")
                return@launch
            }
            
            // 立即检查并刷新令牌
            silentlyRefreshTokenIfNeeded()
            
            // 开始定期检查
            startPeriodicRefresh()
        }
    }
    
    /**
     * 开始定期令牌刷新检查
     */
    private fun startPeriodicRefresh() {
        stopPeriodicRefresh() // 先停止之前的任务
        
        refreshJob = scope.launch {
            while (isActive) {
                delay(TOKEN_REFRESH_INTERVAL)
                
                val authState = authManager.authState.first()
                if (authState.isAuthenticated) {
                    silentlyRefreshTokenIfNeeded()
                } else {
                    Log.d(TAG, "🔍 用户已登出，停止定期刷新")
                    break
                }
            }
        }
        
        Log.d(TAG, "🔄 开始定期令牌刷新检查")
    }
    
    /**
     * 停止定期令牌刷新检查
     */
    private fun stopPeriodicRefresh() {
        refreshJob?.cancel()
        refreshJob = null
        Log.d(TAG, "⏹️ 停止定期令牌刷新检查")
    }
    
    /**
     * 静默刷新令牌（如果需要）- 使用Logto SDK
     */
    private suspend fun silentlyRefreshTokenIfNeeded() {
        if (isAutoRefreshing) {
            Log.d(TAG, "🔄 已在进行静默刷新，跳过重复请求")
            return
        }

        try {
            isAutoRefreshing = true

            // 检查用户是否仍然认证
            if (!logtoTokenManager.isUserAuthenticated()) {
                Log.d(TAG, "🔍 用户未认证，跳过令牌刷新")
                return
            }

            // 检查是否需要刷新令牌
            val needsRefresh = checkIfTokenNeedsRefresh()
            if (!needsRefresh) {
                Log.d(TAG, "🔍 令牌仍然有效，无需刷新")
                return
            }

            Log.i(TAG, "🔄 开始静默刷新令牌（使用Logto SDK）")
            // 使用LogtoTokenManager进行令牌刷新
            val newToken = logtoTokenManager.forceRefreshToken()

            if (newToken != null) {
                Log.i(TAG, "✅ 静默令牌刷新成功，新令牌: ${newToken.take(10)}...")
            } else {
                Log.w(TAG, "⚠️ Logto SDK令牌刷新失败，尝试回退方案")
                // 如果Logto SDK刷新失败，回退到AuthManager
                try {
                    val fallbackToken = authManager.refreshToken()
                    Log.i(TAG, "✅ 回退令牌刷新成功: ${fallbackToken.take(10)}...")
                } catch (fallbackException: Exception) {
                    Log.e(TAG, "❌ 回退令牌刷新也失败: ${fallbackException.message}")
                }
            }

        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 静默令牌刷新失败: ${e.message}")
            // 刷新失败通常意味着refresh token也过期了，需要重新登录
            // AuthManager会处理这种情况
        } finally {
            isAutoRefreshing = false
        }
    }

    /**
     * 检查令牌是否需要刷新
     * 使用与iOS版本相同的逻辑：检查令牌是否在10分钟内过期
     */
    private suspend fun checkIfTokenNeedsRefresh(): Boolean {
        return try {
            val authState = authManager.authState.first()
            if (!authState.isAuthenticated) {
                Log.d(TAG, "🔍 用户未认证，无需刷新令牌")
                return false
            }

            // 使用AuthManager的公共方法检查令牌状态
            val needsRefresh = authManager.isTokenRefreshNeeded()
            Log.d(TAG, "🔍 令牌刷新检查结果: $needsRefresh")
            needsRefresh
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 检查令牌状态失败: ${e.message}")
            true // 检查失败时认为需要刷新
        }
    }
    
    /**
     * 手动触发令牌刷新
     */
    fun manualRefresh() {
        scope.launch {
            Log.i(TAG, "🔄 手动触发令牌刷新")
            silentlyRefreshTokenIfNeeded()
        }
    }
    
    /**
     * 检查当前是否正在刷新
     */
    fun isRefreshing(): Boolean = isAutoRefreshing
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopPeriodicRefresh()
        scope.cancel()
        ProcessLifecycleOwner.get().lifecycle.removeObserver(this)
        Log.i(TAG, "🧹 TokenRefreshManager 资源清理完成")
    }
}
