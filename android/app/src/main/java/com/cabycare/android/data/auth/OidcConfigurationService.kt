package com.cabycare.android.data.auth

import android.util.Log
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.Request
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * OIDC配置服务
 * 按照OpenID Connect Discovery 1.0规范实现
 * 
 * 参考文档：
 * - OpenID Connect Discovery 1.0: https://openid.net/specs/openid-connect-discovery-1_0.html
 */
@Singleton
class OidcConfigurationService @Inject constructor(
    private val okHttpClient: OkHttpClient
) {
    
    companion object {
        private const val TAG = "OidcConfigurationService"
    }
    
    /**
     * OIDC提供者配置响应模型
     * 按照OpenID Connect Discovery 1.0 Section 3定义
     */
    @Serializable
    data class ProviderConfiguration(
        // 必需的元数据
        @SerialName("issuer")
        val issuer: String,
        
        @SerialName("authorization_endpoint")
        val authorizationEndpoint: String,
        
        @SerialName("token_endpoint")
        val tokenEndpoint: String,
        
        @SerialName("userinfo_endpoint")
        val userinfoEndpoint: String? = null,
        
        @SerialName("jwks_uri")
        val jwksUri: String,
        
        @SerialName("response_types_supported")
        val responseTypesSupported: List<String>,
        
        @SerialName("subject_types_supported")
        val subjectTypesSupported: List<String>,
        
        @SerialName("id_token_signing_alg_values_supported")
        val idTokenSigningAlgValuesSupported: List<String>,
        
        // 可选的元数据
        @SerialName("scopes_supported")
        val scopesSupported: List<String>? = null,
        
        @SerialName("response_modes_supported")
        val responseModesSupported: List<String>? = null,
        
        @SerialName("grant_types_supported")
        val grantTypesSupported: List<String>? = null,
        
        @SerialName("token_endpoint_auth_methods_supported")
        val tokenEndpointAuthMethodsSupported: List<String>? = null,
        
        @SerialName("claims_supported")
        val claimsSupported: List<String>? = null,
        
        @SerialName("code_challenge_methods_supported")
        val codeChallengeMethodsSupported: List<String>? = null,
        
        @SerialName("end_session_endpoint")
        val endSessionEndpoint: String? = null,
        
        @SerialName("revocation_endpoint")
        val revocationEndpoint: String? = null,
        
        @SerialName("introspection_endpoint")
        val introspectionEndpoint: String? = null
    )
    
    /**
     * 获取OIDC提供者配置
     * 按照OpenID Connect Discovery 1.0 Section 4实现
     */
    suspend fun getProviderConfiguration(): Result<ProviderConfiguration> = suspendCancellableCoroutine { continuation ->
        
        Log.i(TAG, "🔍 获取OIDC提供者配置")
        Log.d(TAG, "📡 请求URL: ${LogtoConfig.OPENID_CONFIGURATION_ENDPOINT}")
        
        try {
            val request = Request.Builder()
                .url(LogtoConfig.OPENID_CONFIGURATION_ENDPOINT)
                .get()
                .addHeader("Accept", "application/json")
                .build()
            
            okHttpClient.newCall(request).enqueue(object : okhttp3.Callback {
                override fun onFailure(call: okhttp3.Call, e: java.io.IOException) {
                    Log.e(TAG, "❌ 获取OIDC配置网络失败: ${e.message}", e)
                    continuation.resume(Result.failure(e))
                }
                
                override fun onResponse(call: okhttp3.Call, response: okhttp3.Response) {
                    try {
                        val responseBody = response.body?.string() ?: ""
                        Log.d(TAG, "📡 OIDC配置响应: 状态码=${response.code}")
                        
                        if (response.isSuccessful) {
                            Log.d(TAG, "📡 OIDC配置响应内容: $responseBody")
                            
                            val configuration = Json.decodeFromString<ProviderConfiguration>(responseBody)
                            
                            Log.i(TAG, "✅ 获取OIDC配置成功")
                            Log.d(TAG, "🔍 配置详情:")
                            Log.d(TAG, "  - issuer: ${configuration.issuer}")
                            Log.d(TAG, "  - authorization_endpoint: ${configuration.authorizationEndpoint}")
                            Log.d(TAG, "  - token_endpoint: ${configuration.tokenEndpoint}")
                            Log.d(TAG, "  - userinfo_endpoint: ${configuration.userinfoEndpoint}")
                            Log.d(TAG, "  - jwks_uri: ${configuration.jwksUri}")
                            Log.d(TAG, "  - scopes_supported: ${configuration.scopesSupported}")
                            Log.d(TAG, "  - grant_types_supported: ${configuration.grantTypesSupported}")
                            Log.d(TAG, "  - code_challenge_methods_supported: ${configuration.codeChallengeMethodsSupported}")
                            
                            continuation.resume(Result.success(configuration))
                        } else {
                            Log.e(TAG, "❌ 获取OIDC配置失败: HTTP ${response.code}")
                            Log.e(TAG, "❌ 错误响应: $responseBody")
                            
                            val exception = Exception("OIDC configuration request failed: HTTP ${response.code} - $responseBody")
                            continuation.resume(Result.failure(exception))
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 解析OIDC配置响应失败: ${e.message}", e)
                        continuation.resume(Result.failure(e))
                    }
                }
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 构建OIDC配置请求失败: ${e.message}", e)
            continuation.resume(Result.failure(e))
        }
    }
    
    /**
     * 验证OIDC配置是否与本地配置匹配
     */
    suspend fun validateConfiguration(): Result<Boolean> {
        Log.i(TAG, "🔍 验证OIDC配置")
        
        return try {
            val configResult = getProviderConfiguration()
            
            if (configResult.isSuccess) {
                val config = configResult.getOrThrow()
                
                // 验证关键endpoints是否匹配
                val validations = listOf(
                    "issuer" to (config.issuer == LogtoConfig.ISSUER_ENDPOINT),
                    "authorization_endpoint" to (config.authorizationEndpoint == LogtoConfig.AUTHORIZATION_ENDPOINT),
                    "token_endpoint" to (config.tokenEndpoint == LogtoConfig.TOKEN_ENDPOINT),
                    "userinfo_endpoint" to (config.userinfoEndpoint == LogtoConfig.USERINFO_ENDPOINT),
                    "jwks_uri" to (config.jwksUri == LogtoConfig.JWKS_URI)
                )
                
                val allValid = validations.all { it.second }
                
                Log.i(TAG, "🔍 配置验证结果: $allValid")
                validations.forEach { (name, isValid) ->
                    val status = if (isValid) "✅" else "❌"
                    Log.d(TAG, "  $status $name: $isValid")
                }
                
                if (!allValid) {
                    Log.w(TAG, "⚠️ 部分配置不匹配，请检查LogtoConfig中的endpoints")
                }
                
                // 验证支持的功能
                validateSupportedFeatures(config)
                
                Result.success(allValid)
            } else {
                val exception = configResult.exceptionOrNull() ?: Exception("获取OIDC配置失败")
                Log.e(TAG, "❌ 配置验证失败", exception)
                Result.failure(exception)
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 配置验证异常: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 验证支持的功能
     */
    private fun validateSupportedFeatures(config: ProviderConfiguration) {
        Log.d(TAG, "🔍 验证支持的功能")
        
        // 验证支持的响应类型
        val requiredResponseTypes = listOf("code")
        val supportedResponseTypes = config.responseTypesSupported
        val responseTypesSupported = requiredResponseTypes.all { it in supportedResponseTypes }
        Log.d(TAG, "  ${if (responseTypesSupported) "✅" else "❌"} 响应类型支持: $responseTypesSupported")
        
        // 验证支持的授权类型
        val requiredGrantTypes = listOf("authorization_code", "refresh_token")
        val supportedGrantTypes = config.grantTypesSupported ?: emptyList()
        val grantTypesSupported = requiredGrantTypes.all { it in supportedGrantTypes }
        Log.d(TAG, "  ${if (grantTypesSupported) "✅" else "❌"} 授权类型支持: $grantTypesSupported")
        
        // 验证PKCE支持
        val supportedCodeChallengeMethods = config.codeChallengeMethodsSupported ?: emptyList()
        val pkceSupported = "S256" in supportedCodeChallengeMethods
        Log.d(TAG, "  ${if (pkceSupported) "✅" else "❌"} PKCE支持: $pkceSupported")
        
        // 验证支持的作用域
        val requiredScopes = LogtoConfig.SCOPES
        val supportedScopes = config.scopesSupported ?: emptyList()
        val scopesSupported = requiredScopes.all { it in supportedScopes }
        Log.d(TAG, "  ${if (scopesSupported) "✅" else "❌"} 作用域支持: $scopesSupported")
        
        if (!responseTypesSupported || !grantTypesSupported || !pkceSupported) {
            Log.w(TAG, "⚠️ 某些必需功能不受支持，可能影响正常使用")
        }
    }
    
    /**
     * 获取支持的作用域列表
     */
    suspend fun getSupportedScopes(): List<String> {
        return try {
            val configResult = getProviderConfiguration()
            if (configResult.isSuccess) {
                configResult.getOrThrow().scopesSupported ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 获取支持的作用域失败: ${e.message}")
            emptyList()
        }
    }
    
    /**
     * 检查是否支持特定功能
     */
    suspend fun isFeatureSupported(feature: String): Boolean {
        return try {
            val configResult = getProviderConfiguration()
            if (configResult.isSuccess) {
                val config = configResult.getOrThrow()
                when (feature.lowercase()) {
                    "pkce" -> config.codeChallengeMethodsSupported?.contains("S256") == true
                    "refresh_token" -> config.grantTypesSupported?.contains("refresh_token") == true
                    "userinfo" -> config.userinfoEndpoint != null
                    "end_session" -> config.endSessionEndpoint != null
                    "revocation" -> config.revocationEndpoint != null
                    "introspection" -> config.introspectionEndpoint != null
                    else -> false
                }
            } else {
                false
            }
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 检查功能支持失败: ${e.message}")
            false
        }
    }
}
