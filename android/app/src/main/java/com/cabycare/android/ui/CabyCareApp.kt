package com.cabycare.android.ui

import android.util.Log
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.ui.auth.AuthViewModel
import com.cabycare.android.ui.auth.LoginScreen
import com.cabycare.android.ui.main.MainScreen
import com.cabycare.android.ui.splash.SplashScreen
import com.cabycare.android.ui.startup.StartupManager

/**
 * CabyCare应用程序的根Composable
 * 负责管理应用程序的整体导航和状态，支持智能启动检查
 */
@Composable
fun CabyCareApp(
    modifier: Modifier = Modifier,
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val authState by authViewModel.authState.collectAsState()
    val startupState by authViewModel.startupState.collectAsState()
    val startupErrorMessage by authViewModel.startupErrorMessage.collectAsState()

    // 应用启动时执行令牌检查
    LaunchedEffect(Unit) {
        Log.i("CabyCareApp", "🚀 应用启动，开始执行启动检查")
        authViewModel.performStartupCheck()
    }

    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when (startupState) {
            StartupManager.StartupState.INITIALIZING -> {
                // 显示启动画面
                SplashScreen()
            }

            StartupManager.StartupState.CHECKING_TOKEN -> {
                // 显示令牌检查状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    SplashScreen()

                    // 在启动画面上叠加检查状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.BottomCenter
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.padding(bottom = 100.dp)
                        ) {
                            CircularProgressIndicator(
                                color = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "正在验证登录状态...",
                                color = MaterialTheme.colorScheme.onSurface,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }

            StartupManager.StartupState.AUTHENTICATED -> {
                // 令牌有效，直接进入主界面
                Log.i("CabyCareApp", "✅ 令牌验证成功，进入主界面")
                MainScreen()
            }

            StartupManager.StartupState.UNAUTHENTICATED -> {
                // 需要登录
                Log.i("CabyCareApp", "🔑 用户未认证，显示登录界面")
                LoginScreen(
                    onLoginSuccess = {
                        authViewModel.onLoginSuccess()
                    }
                )
            }

            StartupManager.StartupState.ERROR -> {
                // 启动检查出错，显示登录界面
                Log.e("CabyCareApp", "❌ 启动检查出错: $startupErrorMessage")
                LoginScreen(
                    onLoginSuccess = {
                        authViewModel.onLoginSuccess()
                    }
                )
            }
        }

        // 如果AuthManager的loading状态为true，显示额外的加载指示器
        if (authState.isLoading && startupState == StartupManager.StartupState.AUTHENTICATED) {
            CircularProgressIndicator(
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
