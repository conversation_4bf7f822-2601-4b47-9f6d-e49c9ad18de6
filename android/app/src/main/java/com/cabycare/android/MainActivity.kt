package com.cabycare.android

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import com.cabycare.android.data.auth.AuthManager
import com.cabycare.android.ui.CabyCareApp
import com.cabycare.android.ui.theme.CabyCareTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主Activity - 应用程序的入口点
 * 负责设置Compose UI和主题，以及处理Logto回调
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var authManager: AuthManager

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 处理Intent（包括Logto回调）
        handleIntent(intent)

        // 应用启动时立即检查令牌状态
        Log.i(TAG, "📱 应用启动，检查令牌状态")
        // AuthManager在初始化时会自动检查令牌状态，无需额外调用

        setContent {
            CabyCareTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    CabyCareApp(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        intent?.let {
            val action = it.action
            val data = it.data

            Log.d(TAG, "处理Intent: action=$action, data=$data")

            if (action == Intent.ACTION_VIEW && data != null) {
                handleLogtoCallback(data)
            }
        }
    }

    private fun handleLogtoCallback(uri: Uri) {
        Log.i(TAG, "🔄 处理Logto回调: $uri")

        if (uri.scheme == "com.cabycare.android") {
            Log.i(TAG, "✅ 检测到有效的Logto回调URI")

            // Logto SDK会自动处理回调，无需手动处理
            Log.i(TAG, "📝 Logto回调URI已接收，SDK会自动处理")
        } else {
            Log.w(TAG, "⚠️ 无效的回调URI: $uri")
        }
    }
}
