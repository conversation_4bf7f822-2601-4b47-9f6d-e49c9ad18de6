package com.cabycare.android.di

import android.content.Context
import com.cabycare.android.data.auth.TokenInterceptor
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit
import javax.inject.Singleton
import javax.inject.Qualifier

/**
 * 限定符注解，用于区分不同的OkHttpClient实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class BasicOkHttpClient

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class AuthenticatedOkHttpClient

/**
 * 网络模块
 * 提供网络相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    /**
     * 提供基础OkHttpClient实例（用于AuthManager等不需要令牌拦截的组件）
     */
    @Provides
    @Singleton
    @BasicOkHttpClient
    fun provideBasicOkHttpClient(): OkHttpClient {
        val logging = HttpLoggingInterceptor()
        logging.level = HttpLoggingInterceptor.Level.BODY

        return OkHttpClient.Builder()
            .addInterceptor(logging)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }

    /**
     * 提供带认证的OkHttpClient实例（包含令牌自动刷新功能）
     */
    @Provides
    @Singleton
    @AuthenticatedOkHttpClient
    fun provideAuthenticatedOkHttpClient(tokenInterceptor: TokenInterceptor): OkHttpClient {
        val logging = HttpLoggingInterceptor()
        logging.level = HttpLoggingInterceptor.Level.BODY

        return OkHttpClient.Builder()
            .addInterceptor(tokenInterceptor) // 添加令牌拦截器（必须在日志拦截器之前）
            .addInterceptor(logging)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }



    /**
     * 提供默认OkHttpClient实例（向后兼容）
     */
    @Provides
    @Singleton
    fun provideOkHttpClient(@AuthenticatedOkHttpClient authenticatedClient: OkHttpClient): OkHttpClient {
        return authenticatedClient
    }

    /**
     * 提供NetworkManager实例
     */
    @Provides
    @Singleton
    fun provideNetworkManager(userPreferences: UserPreferences): NetworkManager {
        return NetworkManager(userPreferences)
    }

    /**
     * 提供ApiService实例
     * 直接使用NetworkManager创建，避免循环依赖
     */
    @Provides
    @Singleton
    fun provideApiService(networkManager: NetworkManager): ApiService {
        return networkManager.createApiService<ApiService>()
    }

    /**
     * 提供BluetoothManager实例
     */
    @Provides
    @Singleton
    fun provideBluetoothManager(
        @ApplicationContext context: Context
    ): com.cabycare.android.bluetooth.BluetoothManager {
        return com.cabycare.android.bluetooth.BluetoothManager(context)
    }
}
