package com.cabycare.android.data.auth

import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 令牌拦截器
 * 自动为请求添加认证头，并处理令牌过期的情况
 * 使用OIDC标准令牌管理机制，支持Logto SDK备用
 */
@Singleton
class TokenInterceptor @Inject constructor(
    private val logtoManager: LogtoManager,
    private val userPreferences: UserPreferences,
    private val oidcUserInfoService: OidcUserInfoService
) : Interceptor {
    
    companion object {
        private const val TAG = "TokenInterceptor"
        private const val AUTHORIZATION_HEADER = "Authorization"
        private const val BEARER_PREFIX = "Bearer "
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // 如果请求已经包含Authorization头，直接执行
        if (originalRequest.header(AUTHORIZATION_HEADER) != null) {
            return chain.proceed(originalRequest)
        }

        // 获取存储的访问令牌
        val accessToken = runBlocking {
            try {
                userPreferences.getAccessToken().first()
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 获取存储的访问令牌失败: ${e.message}")
                null
            }
        }

        // 如果没有令牌，直接执行原始请求
        if (accessToken.isNullOrEmpty()) {
            Log.d(TAG, "🔍 没有访问令牌，执行原始请求")
            return chain.proceed(originalRequest)
        }
        
        // 添加认证头
        val authenticatedRequest = originalRequest.newBuilder()
            .addHeader(AUTHORIZATION_HEADER, "$BEARER_PREFIX$accessToken")
            .build()
        
        Log.d(TAG, "🔑 为请求添加认证头: ${originalRequest.url}")
        
        // 执行请求
        val response = chain.proceed(authenticatedRequest)
        
        // 检查是否是认证失败（401 Unauthorized）
        if (response.code == 401) {
            Log.w(TAG, "🔒 收到401响应，令牌可能已过期")

            // 首先验证当前令牌是否真的过期
            val isTokenValid = runBlocking {
                try {
                    Log.d(TAG, "🔍 验证当前令牌有效性")
                    oidcUserInfoService.validateAccessToken(accessToken)
                } catch (e: Exception) {
                    Log.w(TAG, "⚠️ 令牌验证失败: ${e.message}")
                    false
                }
            }

            if (!isTokenValid) {
                Log.i(TAG, "🔄 令牌已过期，尝试使用多种方式刷新")

                // 尝试多种刷新方式
                val newToken = runBlocking {
                    try {
                        // 方式1：使用OIDC标准刷新流程
                        Log.d(TAG, "🔄 尝试OIDC标准刷新流程")
                        val refreshToken = userPreferences.getRefreshToken().first()
                        if (!refreshToken.isNullOrEmpty()) {
                            // 这里需要注入AuthManager或OidcTokenService
                            // 暂时使用Logto SDK作为备用
                            Log.d(TAG, "🔄 回退到Logto SDK刷新")
                            logtoManager.getAccessToken()
                        } else {
                            Log.w(TAG, "⚠️ 没有刷新令牌，使用Logto SDK")
                            logtoManager.getAccessToken()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 所有刷新方式都失败: ${e.message}")
                        null
                    }
                }

                // 如果刷新成功，重试原始请求
                if (!newToken.isNullOrEmpty()) {
                    Log.i(TAG, "✅ 令牌刷新成功，重试请求")
                    response.close() // 关闭原始响应

                    // 保存新令牌
                    runBlocking {
                        try {
                            userPreferences.saveAccessToken(newToken)
                        } catch (e: Exception) {
                            Log.w(TAG, "⚠️ 保存新令牌失败: ${e.message}")
                        }
                    }

                    val retryRequest = originalRequest.newBuilder()
                        .addHeader(AUTHORIZATION_HEADER, "$BEARER_PREFIX$newToken")
                        .build()

                    return chain.proceed(retryRequest)
                } else {
                    Log.e(TAG, "❌ 令牌刷新失败，返回401响应")
                }
            } else {
                Log.d(TAG, "🔍 令牌仍然有效，401可能是权限问题")
            }
        }
        
        return response
    }
}
