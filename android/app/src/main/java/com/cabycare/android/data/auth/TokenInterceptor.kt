package com.cabycare.android.data.auth

import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 令牌拦截器
 * 自动为请求添加认证头，并处理令牌过期的情况
 * 使用简化的令牌管理，避免循环依赖
 */
@Singleton
class TokenInterceptor @Inject constructor(
    private val userPreferences: UserPreferences,
    private val oidcTokenService: OidcTokenService
) : Interceptor {
    
    companion object {
        private const val TAG = "TokenInterceptor"
        private const val AUTHORIZATION_HEADER = "Authorization"
        private const val BEARER_PREFIX = "Bearer "
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // 如果请求已经包含Authorization头，直接执行
        if (originalRequest.header(AUTHORIZATION_HEADER) != null) {
            return chain.proceed(originalRequest)
        }

        // 获取存储的访问令牌
        val accessToken = runBlocking {
            try {
                userPreferences.getAccessToken().first()
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 获取存储的访问令牌失败: ${e.message}")
                null
            }
        }

        // 如果没有令牌，直接执行原始请求
        if (accessToken.isNullOrEmpty()) {
            Log.d(TAG, "🔍 没有访问令牌，执行原始请求")
            return chain.proceed(originalRequest)
        }
        
        // 添加认证头
        val authenticatedRequest = originalRequest.newBuilder()
            .addHeader(AUTHORIZATION_HEADER, "$BEARER_PREFIX$accessToken")
            .build()
        
        Log.d(TAG, "🔑 为请求添加认证头: ${originalRequest.url}")
        
        // 执行请求
        val response = chain.proceed(authenticatedRequest)
        
        // 检查是否是认证失败（401 Unauthorized）
        if (response.code == 401) {
            Log.w(TAG, "🔒 收到401响应，令牌可能已过期，尝试刷新")

            // 尝试使用OIDC标准刷新流程
            val newToken = runBlocking {
                try {
                    Log.d(TAG, "🔄 尝试OIDC标准刷新流程")
                    val refreshToken = userPreferences.getRefreshToken().first()
                    if (!refreshToken.isNullOrEmpty()) {
                        Log.d(TAG, "🔄 使用刷新令牌: ${refreshToken.take(20)}...")
                        val tokenResult = oidcTokenService.refreshAccessToken(refreshToken)
                        if (tokenResult.isSuccess) {
                            val tokenResponse = tokenResult.getOrThrow()
                            Log.i(TAG, "✅ OIDC令牌刷新成功")

                            // 保存新令牌
                            userPreferences.saveAccessToken(tokenResponse.accessToken)
                            tokenResponse.refreshToken?.let { newRefreshToken ->
                                userPreferences.saveRefreshToken(newRefreshToken)
                            }
                            tokenResponse.expiresIn?.let { expiresIn ->
                                val expiresAt = System.currentTimeMillis() + (expiresIn * 1000)
                                userPreferences.saveTokenExpiresAt(expiresAt)
                            }

                            tokenResponse.accessToken
                        } else {
                            Log.e(TAG, "❌ OIDC令牌刷新失败")
                            null
                        }
                    } else {
                        Log.w(TAG, "⚠️ 没有刷新令牌，无法刷新")
                        null
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 令牌刷新异常: ${e.message}")
                    null
                }
            }

            // 如果刷新成功，重试原始请求
            if (!newToken.isNullOrEmpty()) {
                Log.i(TAG, "✅ 令牌刷新成功，重试请求")
                response.close() // 关闭原始响应

                val retryRequest = originalRequest.newBuilder()
                    .addHeader(AUTHORIZATION_HEADER, "$BEARER_PREFIX$newToken")
                    .build()

                return chain.proceed(retryRequest)
            } else {
                Log.e(TAG, "❌ 令牌刷新失败，返回401响应")
            }
        }
        
        return response
    }
}
