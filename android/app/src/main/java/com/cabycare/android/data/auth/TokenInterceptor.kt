package com.cabycare.android.data.auth

import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 令牌拦截器
 * 自动为请求添加认证头，并处理令牌过期的情况
 * 使用Logto SDK内部的令牌管理机制
 */
@Singleton
class TokenInterceptor @Inject constructor(
    private val logtoManager: LogtoManager
) : Interceptor {
    
    companion object {
        private const val TAG = "TokenInterceptor"
        private const val AUTHORIZATION_HEADER = "Authorization"
        private const val BEARER_PREFIX = "Bearer "
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // 如果请求已经包含Authorization头，直接执行
        if (originalRequest.header(AUTHORIZATION_HEADER) != null) {
            return chain.proceed(originalRequest)
        }

        // 检查用户是否已认证
        if (!logtoManager.isAuthenticated) {
            Log.d(TAG, "🔍 用户未认证，执行原始请求")
            return chain.proceed(originalRequest)
        }

        // 使用Logto SDK获取访问令牌（SDK会自动处理刷新）
        val accessToken = runBlocking {
            try {
                logtoManager.getAccessToken()
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 获取访问令牌失败: ${e.message}")
                null
            }
        }

        // 如果没有令牌，直接执行原始请求
        if (accessToken.isNullOrEmpty()) {
            Log.d(TAG, "🔍 没有访问令牌，执行原始请求")
            return chain.proceed(originalRequest)
        }
        
        // 添加认证头
        val authenticatedRequest = originalRequest.newBuilder()
            .addHeader(AUTHORIZATION_HEADER, "$BEARER_PREFIX$accessToken")
            .build()
        
        Log.d(TAG, "🔑 为请求添加认证头: ${originalRequest.url}")
        
        // 执行请求
        val response = chain.proceed(authenticatedRequest)
        
        // 检查是否是认证失败（401 Unauthorized）
        if (response.code == 401) {
            Log.w(TAG, "🔒 收到401响应，令牌可能已过期")

            // 使用Logto SDK尝试获取新令牌（SDK会自动刷新）
            val newToken = runBlocking {
                try {
                    Log.i(TAG, "🔄 使用Logto SDK重新获取令牌")
                    logtoManager.getAccessToken()
                } catch (e: Exception) {
                    Log.e(TAG, "❌ Logto SDK获取令牌失败: ${e.message}")
                    null
                }
            }

            // 如果获取成功，重试原始请求
            if (!newToken.isNullOrEmpty()) {
                Log.i(TAG, "✅ 获取新令牌成功，重试请求")
                response.close() // 关闭原始响应

                val retryRequest = originalRequest.newBuilder()
                    .addHeader(AUTHORIZATION_HEADER, "$BEARER_PREFIX$newToken")
                    .build()

                return chain.proceed(retryRequest)
            } else {
                Log.e(TAG, "❌ 获取新令牌失败，返回401响应")
            }
        }
        
        return response
    }
}
