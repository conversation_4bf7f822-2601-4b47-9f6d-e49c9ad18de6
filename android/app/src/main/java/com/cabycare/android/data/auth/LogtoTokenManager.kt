package com.cabycare.android.data.auth

import android.util.Log
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * Logto令牌管理器
 * 专门处理Logto SDK的令牌获取和刷新逻辑
 * 提供统一的令牌管理接口，确保令牌始终有效
 */
@Singleton
class LogtoTokenManager @Inject constructor(
    private val logtoManager: LogtoManager
) {
    
    companion object {
        private const val TAG = "LogtoTokenManager"
    }
    
    /**
     * 获取有效的访问令牌
     * 如果令牌过期，Logto SDK会自动使用refresh token刷新
     * 
     * @return 有效的访问令牌，如果获取失败返回null
     */
    suspend fun getValidAccessToken(): String? {
        return try {
            Log.d(TAG, "🔍 获取有效的访问令牌")
            
            // 首先检查是否已认证
            if (!logtoManager.isAuthenticated) {
                Log.w(TAG, "⚠️ 用户未认证，无法获取令牌")
                return null
            }
            
            // 使用LogtoManager的getValidAccessToken方法
            // 这个方法会自动处理令牌刷新
            val token = logtoManager.getValidAccessToken()
            
            if (token != null) {
                Log.d(TAG, "✅ 成功获取有效访问令牌: ${token.take(20)}...")
            } else {
                Log.w(TAG, "⚠️ 获取访问令牌失败")
            }
            
            token
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取有效访问令牌时发生异常: ${e.message}", e)
            null
        }
    }
    
    /**
     * 强制刷新访问令牌
     * 主动触发Logto SDK的令牌刷新机制
     * 
     * @return 新的访问令牌，如果刷新失败返回null
     */
    suspend fun forceRefreshToken(): String? {
        return try {
            Log.i(TAG, "🔄 强制刷新访问令牌")
            
            if (!logtoManager.isAuthenticated) {
                Log.w(TAG, "⚠️ 用户未认证，无法刷新令牌")
                return null
            }
            
            // 使用LogtoManager的refreshAccessToken方法
            val newToken = logtoManager.refreshAccessToken()
            
            if (newToken != null) {
                Log.i(TAG, "✅ 令牌刷新成功: ${newToken.take(20)}...")
            } else {
                Log.w(TAG, "⚠️ 令牌刷新失败")
            }
            
            newToken
        } catch (e: Exception) {
            Log.e(TAG, "❌ 强制刷新令牌时发生异常: ${e.message}", e)
            null
        }
    }
    
    /**
     * 检查用户是否已认证
     * 
     * @return true如果用户已认证，false否则
     */
    fun isUserAuthenticated(): Boolean {
        val isAuth = logtoManager.isAuthenticated
        Log.d(TAG, "🔍 检查用户认证状态: $isAuth")
        return isAuth
    }
    
    /**
     * 获取用户信息
     * 
     * @return 用户信息Map，如果获取失败返回null
     */
    suspend fun getUserInfo(): Map<String, Any>? {
        return try {
            Log.d(TAG, "👤 获取用户信息")
            
            if (!logtoManager.isAuthenticated) {
                Log.w(TAG, "⚠️ 用户未认证，无法获取用户信息")
                return null
            }
            
            val userInfo = logtoManager.getUserInfo()
            
            if (userInfo != null) {
                Log.d(TAG, "✅ 成功获取用户信息")
            } else {
                Log.w(TAG, "⚠️ 获取用户信息失败")
            }
            
            userInfo
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取用户信息时发生异常: ${e.message}", e)
            null
        }
    }
    
    /**
     * 安全地获取访问令牌，带重试机制
     * 如果第一次获取失败，会尝试刷新后再次获取
     * 
     * @param maxRetries 最大重试次数，默认为1
     * @return 有效的访问令牌，如果获取失败返回null
     */
    suspend fun getAccessTokenWithRetry(maxRetries: Int = 1): String? {
        var retryCount = 0
        
        while (retryCount <= maxRetries) {
            try {
                Log.d(TAG, "🔍 尝试获取访问令牌 (重试次数: $retryCount/$maxRetries)")
                
                val token = getValidAccessToken()
                if (token != null) {
                    Log.d(TAG, "✅ 成功获取访问令牌")
                    return token
                }
                
                // 如果获取失败且还有重试次数，尝试强制刷新
                if (retryCount < maxRetries) {
                    Log.i(TAG, "🔄 获取令牌失败，尝试强制刷新")
                    forceRefreshToken()
                }
                
                retryCount++
            } catch (e: Exception) {
                Log.e(TAG, "❌ 获取访问令牌时发生异常 (重试次数: $retryCount): ${e.message}", e)
                retryCount++
                
                if (retryCount > maxRetries) {
                    break
                }
            }
        }
        
        Log.e(TAG, "❌ 获取访问令牌失败，已达到最大重试次数")
        return null
    }
}
