package com.cabycare.android.ui.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.auth.AuthManager
import com.cabycare.android.data.auth.AuthState
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.ui.startup.StartupManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 认证ViewModel
 * 负责处理认证相关的UI逻辑
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authManager: AuthManager,
    private val startupManager: StartupManager
) : ViewModel() {

    val authState: StateFlow<AuthState> = authManager.authState
    val startupState: StateFlow<StartupManager.StartupState> = startupManager.startupState
    val startupErrorMessage: StateFlow<String?> = startupManager.errorMessage
    
    /**
     * 原生Logto登录
     */
    fun signInNative(activity: android.app.Activity) {
        viewModelScope.launch {
            authManager.signInNative(activity)
        }
    }


    
    /**
     * 登出
     */
    fun logout() {
        viewModelScope.launch {
            authManager.logout()
        }
    }
    
    /**
     * 刷新认证状态
     */
    fun refreshAuthState() {
        viewModelScope.launch {
            authManager.refreshAuthState()
        }
    }
    
    /**
     * 执行启动检查
     */
    fun performStartupCheck() {
        viewModelScope.launch {
            startupManager.performStartupCheck()
        }
    }

    /**
     * 重置启动状态
     */
    fun resetStartupState() {
        startupManager.resetStartupState()
    }

    /**
     * 登录成功后更新启动状态
     */
    fun onLoginSuccess() {
        startupManager.setAuthenticated()
        refreshAuthState()
    }

    /**
     * 登出后更新启动状态
     */
    fun onLogout() {
        startupManager.setUnauthenticated()
        logout()
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        // 可以在AuthManager中添加clearError方法
    }
}
