package com.cabycare.android.data.auth

import android.util.Log
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.Request
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * OIDC用户信息服务
 * 按照OpenID Connect Core 1.0 Section 5.3实现UserInfo Endpoint
 * 
 * 参考文档：
 * - OpenID Connect Core 1.0 Section 5.3: https://openid.net/specs/openid-connect-core-1_0.html#UserInfo
 */
@Singleton
class OidcUserInfoService @Inject constructor(
    private val okHttpClient: OkHttpClient
) {
    
    companion object {
        private const val TAG = "OidcUserInfoService"
    }
    
    /**
     * OIDC用户信息响应模型
     * 按照OpenID Connect Core 1.0 Section 5.1定义的标准Claims
     */
    @Serializable
    data class UserInfoResponse(
        // Subject Identifier (必需)
        @SerialName("sub")
        val sub: String,
        
        // 标准Profile Claims
        @SerialName("name")
        val name: String? = null,
        
        @SerialName("given_name")
        val givenName: String? = null,
        
        @SerialName("family_name")
        val familyName: String? = null,
        
        @SerialName("middle_name")
        val middleName: String? = null,
        
        @SerialName("nickname")
        val nickname: String? = null,
        
        @SerialName("preferred_username")
        val preferredUsername: String? = null,
        
        @SerialName("profile")
        val profile: String? = null,
        
        @SerialName("picture")
        val picture: String? = null,
        
        @SerialName("website")
        val website: String? = null,
        
        @SerialName("gender")
        val gender: String? = null,
        
        @SerialName("birthdate")
        val birthdate: String? = null,
        
        @SerialName("zoneinfo")
        val zoneinfo: String? = null,
        
        @SerialName("locale")
        val locale: String? = null,
        
        @SerialName("updated_at")
        val updatedAt: Long? = null,
        
        // Email Claims
        @SerialName("email")
        val email: String? = null,
        
        @SerialName("email_verified")
        val emailVerified: Boolean? = null,
        
        // Phone Claims
        @SerialName("phone_number")
        val phoneNumber: String? = null,
        
        @SerialName("phone_number_verified")
        val phoneNumberVerified: Boolean? = null,
        
        // Address Claim
        @SerialName("address")
        val address: AddressClaim? = null
    )
    
    /**
     * 地址信息
     * 按照OpenID Connect Core 1.0 Section 5.1.1定义
     */
    @Serializable
    data class AddressClaim(
        @SerialName("formatted")
        val formatted: String? = null,
        
        @SerialName("street_address")
        val streetAddress: String? = null,
        
        @SerialName("locality")
        val locality: String? = null,
        
        @SerialName("region")
        val region: String? = null,
        
        @SerialName("postal_code")
        val postalCode: String? = null,
        
        @SerialName("country")
        val country: String? = null
    )
    
    /**
     * 获取用户信息
     * 按照OpenID Connect Core 1.0 Section 5.3.1实现
     * 
     * @param accessToken 有效的访问令牌
     * @return 用户信息或错误
     */
    suspend fun getUserInfo(accessToken: String): Result<UserInfoResponse> = suspendCancellableCoroutine { continuation ->
        
        Log.i(TAG, "👤 获取OIDC用户信息")
        Log.d(TAG, "  - access_token: ${accessToken.take(20)}...")
        
        try {
            // 构建UserInfo请求 (OpenID Connect Core 1.0 Section 5.3.1)
            val request = Request.Builder()
                .url(LogtoConfig.USERINFO_ENDPOINT)
                .get()
                .addHeader("Authorization", "Bearer $accessToken")
                .addHeader("Accept", "application/json")
                .build()
            
            Log.d(TAG, "📡 发送用户信息请求到: ${LogtoConfig.USERINFO_ENDPOINT}")
            
            okHttpClient.newCall(request).enqueue(object : okhttp3.Callback {
                override fun onFailure(call: okhttp3.Call, e: java.io.IOException) {
                    Log.e(TAG, "❌ 用户信息请求网络失败: ${e.message}", e)
                    continuation.resume(Result.failure(e))
                }
                
                override fun onResponse(call: okhttp3.Call, response: okhttp3.Response) {
                    try {
                        val responseBody = response.body?.string() ?: ""
                        Log.d(TAG, "📡 用户信息响应: 状态码=${response.code}")
                        
                        if (response.isSuccessful) {
                            Log.d(TAG, "📡 用户信息响应内容: $responseBody")
                            
                            val userInfo = Json.decodeFromString<UserInfoResponse>(responseBody)
                            
                            Log.i(TAG, "✅ 获取用户信息成功")
                            Log.d(TAG, "👤 用户信息详情:")
                            Log.d(TAG, "  - sub: ${userInfo.sub}")
                            Log.d(TAG, "  - name: ${userInfo.name}")
                            Log.d(TAG, "  - email: ${userInfo.email}")
                            Log.d(TAG, "  - email_verified: ${userInfo.emailVerified}")
                            Log.d(TAG, "  - preferred_username: ${userInfo.preferredUsername}")
                            Log.d(TAG, "  - picture: ${userInfo.picture}")
                            
                            continuation.resume(Result.success(userInfo))
                        } else {
                            Log.e(TAG, "❌ 获取用户信息失败: HTTP ${response.code}")
                            Log.e(TAG, "❌ 错误响应: $responseBody")
                            
                            val exception = when (response.code) {
                                401 -> Exception("Access token expired or invalid")
                                403 -> Exception("Insufficient scope for UserInfo endpoint")
                                else -> Exception("UserInfo request failed: HTTP ${response.code} - $responseBody")
                            }
                            
                            continuation.resume(Result.failure(exception))
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 解析用户信息响应失败: ${e.message}", e)
                        continuation.resume(Result.failure(e))
                    }
                }
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 构建用户信息请求失败: ${e.message}", e)
            continuation.resume(Result.failure(e))
        }
    }
    
    /**
     * 验证访问令牌是否有效
     * 通过调用UserInfo端点来验证令牌
     * 
     * @param accessToken 要验证的访问令牌
     * @return 令牌是否有效
     */
    suspend fun validateAccessToken(accessToken: String): Boolean {
        Log.d(TAG, "🔍 验证访问令牌有效性")
        
        return try {
            val result = getUserInfo(accessToken)
            val isValid = result.isSuccess
            
            Log.d(TAG, "🔍 令牌验证结果: $isValid")
            isValid
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 令牌验证失败: ${e.message}")
            false
        }
    }
    
    /**
     * 从用户信息中提取显示名称
     * 按照优先级：name > preferred_username > email > sub
     */
    fun extractDisplayName(userInfo: UserInfoResponse): String {
        return userInfo.name
            ?: userInfo.preferredUsername
            ?: userInfo.email
            ?: userInfo.sub
    }
    
    /**
     * 检查用户邮箱是否已验证
     */
    fun isEmailVerified(userInfo: UserInfoResponse): Boolean {
        return userInfo.emailVerified == true
    }
    
    /**
     * 检查用户手机号是否已验证
     */
    fun isPhoneVerified(userInfo: UserInfoResponse): Boolean {
        return userInfo.phoneNumberVerified == true
    }
}
