package com.cabycare.android.ui.startup

import android.util.Log
import com.cabycare.android.data.auth.AuthManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 启动状态管理器
 * 负责管理应用启动时的状态检查和导航逻辑
 */
@Singleton
class StartupManager @Inject constructor(
    private val authManager: AuthManager
) {
    
    companion object {
        private const val TAG = "StartupManager"
    }
    
    /**
     * 启动状态枚举
     */
    enum class StartupState {
        INITIALIZING,    // 初始化中
        CHECKING_TOKEN,  // 检查令牌中
        AUTHENTICATED,   // 已认证，可以进入主界面
        UNAUTHENTICATED, // 未认证，需要登录
        ERROR           // 发生错误
    }
    
    private val _startupState = MutableStateFlow(StartupState.INITIALIZING)
    val startupState: StateFlow<StartupState> = _startupState.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    /**
     * 执行启动检查
     * 这是应用启动时的主要入口点
     */
    suspend fun performStartupCheck() {
        try {
            Log.i(TAG, "🚀 开始执行启动检查")
            _startupState.value = StartupState.CHECKING_TOKEN
            _errorMessage.value = null
            
            // 首先进行快速检查
            val quickCheckResult = authManager.quickStartupTokenCheck()
            
            if (quickCheckResult) {
                Log.i(TAG, "⚡ 快速检查通过，用户已认证")
                _startupState.value = StartupState.AUTHENTICATED
                return
            }
            
            Log.d(TAG, "🔍 快速检查未通过，进行完整的令牌验证")
            
            // 进行完整的令牌验证（包括网络验证和自动刷新）
            val fullCheckResult = authManager.checkStartupTokenStatus()
            
            if (fullCheckResult) {
                Log.i(TAG, "✅ 完整检查通过，用户已认证")
                _startupState.value = StartupState.AUTHENTICATED
            } else {
                Log.i(TAG, "❌ 令牌验证失败，用户需要重新登录")
                _startupState.value = StartupState.UNAUTHENTICATED
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 启动检查过程中发生异常: ${e.message}", e)
            _errorMessage.value = "启动检查失败: ${e.message}"
            _startupState.value = StartupState.ERROR
        }
    }
    
    /**
     * 重置启动状态
     */
    fun resetStartupState() {
        Log.d(TAG, "🔄 重置启动状态")
        _startupState.value = StartupState.INITIALIZING
        _errorMessage.value = null
    }
    
    /**
     * 手动设置为已认证状态
     * 用于登录成功后的状态更新
     */
    fun setAuthenticated() {
        Log.i(TAG, "🔑 手动设置为已认证状态")
        _startupState.value = StartupState.AUTHENTICATED
        _errorMessage.value = null
    }
    
    /**
     * 手动设置为未认证状态
     * 用于登出后的状态更新
     */
    fun setUnauthenticated() {
        Log.i(TAG, "🚪 手动设置为未认证状态")
        _startupState.value = StartupState.UNAUTHENTICATED
        _errorMessage.value = null
    }
    
    /**
     * 检查是否应该显示启动画面
     */
    fun shouldShowSplash(): Boolean {
        return _startupState.value in listOf(
            StartupState.INITIALIZING,
            StartupState.CHECKING_TOKEN
        )
    }
    
    /**
     * 检查是否应该显示主界面
     */
    fun shouldShowMainScreen(): Boolean {
        return _startupState.value == StartupState.AUTHENTICATED
    }
    
    /**
     * 检查是否应该显示登录界面
     */
    fun shouldShowLoginScreen(): Boolean {
        return _startupState.value in listOf(
            StartupState.UNAUTHENTICATED,
            StartupState.ERROR
        )
    }
    
    /**
     * 获取当前状态的描述信息
     */
    fun getStateDescription(): String {
        return when (_startupState.value) {
            StartupState.INITIALIZING -> "正在初始化..."
            StartupState.CHECKING_TOKEN -> "正在验证登录状态..."
            StartupState.AUTHENTICATED -> "已登录"
            StartupState.UNAUTHENTICATED -> "未登录"
            StartupState.ERROR -> "启动检查失败: ${_errorMessage.value ?: "未知错误"}"
        }
    }
}
