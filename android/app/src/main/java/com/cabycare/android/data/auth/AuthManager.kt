package com.cabycare.android.data.auth

import android.content.Context
import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.model.OAuthCallbackResponse
import com.cabycare.android.data.model.TokenRefreshResponse
import com.cabycare.android.data.model.User
import com.cabycare.android.data.model.UserInfoResponse
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.network.safeApiCall
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.Base64
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton
import com.cabycare.android.di.BasicOkHttpClient
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.long

/**
 * 认证状态数据类
 */
data class AuthState(
    val isAuthenticated: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null,
    val user: User? = null
)

/**
 * 认证管理器
 * 负责用户认证、令牌管理和自动刷新
 */
@Singleton
class AuthManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userPreferences: UserPreferences,
    private val apiService: ApiService,
    @BasicOkHttpClient private val okHttpClient: OkHttpClient,
    private val logtoManager: LogtoManager,
    private val oidcTokenService: OidcTokenService,
    private val oidcUserInfoService: OidcUserInfoService
) {
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    companion object {
        private const val TAG = "AuthManager"
        private const val TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000L // 5分钟
    }
    
    private val _authState = MutableStateFlow(AuthState())
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    private val refreshMutex = Mutex()
    private var refreshTask: kotlinx.coroutines.Deferred<String>? = null
    
    init {
        // 调试信息
        com.cabycare.android.debug.LogtoDebugHelper.printConfig()
        com.cabycare.android.debug.LogtoDebugHelper.checkConfiguration()

        // 初始化时检查认证状态
        checkInitialAuthStatus()

        // 添加额外的调试信息
        scope.launch {
            Log.i(TAG, "🔍 AuthManager初始化完成，开始详细检查...")

            try {
                val isAuth = userPreferences.isAuthenticated().first()
                val token = userPreferences.getAccessToken().first()
                val refreshToken = userPreferences.getRefreshToken().first()
                val expiresAt = userPreferences.getTokenExpiresAt().first()

                Log.i(TAG, "🔍 详细检查结果:")
                Log.i(TAG, "  - isAuthenticated: $isAuth")
                Log.i(TAG, "  - hasAccessToken: ${!token.isNullOrEmpty()}")
                Log.i(TAG, "  - hasRefreshToken: ${!refreshToken.isNullOrEmpty()}")
                Log.i(TAG, "  - tokenExpiresAt: $expiresAt")

                if (token != null && token.isNotEmpty()) {
                    Log.i(TAG, "  - accessToken前缀: ${token.take(20)}...")
                    Log.i(TAG, "  - accessToken长度: ${token.length}")
                }

                if (expiresAt != null) {
                    val currentTime = System.currentTimeMillis()
                    val isExpired = expiresAt <= currentTime
                    Log.i(TAG, "  - 过期时间: ${java.util.Date(expiresAt)}")
                    Log.i(TAG, "  - 当前时间: ${java.util.Date(currentTime)}")
                    Log.i(TAG, "  - 是否已过期: $isExpired")
                }

            } catch (e: Exception) {
                Log.e(TAG, "🔍 详细检查失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 检查初始认证状态
     * 应用启动时立即检查令牌状态，如果令牌有效则直接进入主界面
     */
    private fun checkInitialAuthStatus() {
        // 先将认证状态设置为加载中
        updateAuthState(isLoading = true)

        scope.launch {
            Log.i(TAG, "🔍 开始检查初始认证状态")

            // 打印存储的认证数据用于调试
            userPreferences.printAuthData()

            val isAuthenticated = userPreferences.isAuthenticated().first()
            val accessToken = userPreferences.getAccessToken().first()
            val refreshToken = userPreferences.getRefreshToken().first()

            Log.d(TAG, "🔍 存储状态检查:")
            Log.d(TAG, "  - isAuthenticated: $isAuthenticated")
            Log.d(TAG, "  - accessToken存在: ${!accessToken.isNullOrEmpty()}")
            Log.d(TAG, "  - refreshToken存在: ${!refreshToken.isNullOrEmpty()}")

            if (accessToken != null) {
                Log.d(TAG, "  - accessToken前缀: ${accessToken.take(20)}...")
            }
            if (refreshToken != null) {
                Log.d(TAG, "  - refreshToken前缀: ${refreshToken.take(20)}...")
            }

            if (isAuthenticated && !accessToken.isNullOrEmpty()) {
                Log.i(TAG, "🔍 检查存储的访问令牌状态")

                // 检查令牌格式和有效性
                if (accessToken.length < 100) {
                    Log.w(TAG, "⚠️ 访问令牌长度异常（${accessToken.length}），可能不是标准JWT格式")
                    Log.w(TAG, "⚠️ 这可能是Logto SDK的内部令牌，需要重新登录获取OIDC令牌")
                    clearAuthCredentials()
                    updateAuthState(isAuthenticated = false, isLoading = false)
                } else if (isTokenValid(accessToken)) {
                    Log.i(TAG, "🔑 发现有效的访问令牌，用户已登录")
                    updateAuthState(isAuthenticated = true, isLoading = false)
                    loadUserProfile()
                } else if (shouldRefreshToken(accessToken)) {
                    Log.w(TAG, "⚠️ 访问令牌即将过期或已过期，尝试刷新")
                    try {
                        val newToken = refreshToken()
                        Log.i(TAG, "✅ 令牌刷新成功，用户保持登录状态")
                        updateAuthState(isAuthenticated = true, isLoading = false)
                        loadUserProfile()
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 令牌刷新失败，需要重新登录", e)
                        clearAuthCredentials()
                        updateAuthState(isAuthenticated = false, isLoading = false)
                    }
                } else {
                    Log.e(TAG, "❌ 访问令牌无效且无法刷新，清除认证信息")
                    clearAuthCredentials()
                    updateAuthState(isAuthenticated = false, isLoading = false)
                }
            } else {
                if (!isAuthenticated) {
                    Log.i(TAG, "🔒 用户未登录 - isAuthenticated为false")
                } else if (accessToken.isNullOrEmpty()) {
                    Log.i(TAG, "🔒 用户未登录 - accessToken为空")
                }

                Log.w(TAG, "⚠️ 没有找到有效的认证数据")
                Log.w(TAG, "⚠️ 可能的原因:")
                Log.w(TAG, "  1. 用户从未登录过")
                Log.w(TAG, "  2. 应用数据被清除")
                Log.w(TAG, "  3. 令牌存储失败")
                Log.w(TAG, "  4. DataStore读取失败")

                updateAuthState(isAuthenticated = false, isLoading = false)
            }
        }
    }
    
    /**
     * 检查令牌是否有效（不考虑刷新阈值，只检查是否过期）
     */
    private fun isTokenValid(token: String): Boolean {
        Log.i(TAG, "🔍 开始检查令牌有效性...")

        return try {
            val parts = token.split(".")
            if (parts.size != 3) {
                Log.e(TAG, "❌ 令牌格式无效: 不是有效的JWT格式（应有3部分）")
                return false
            }

            Log.d(TAG, "🔍 JWT格式有效，解析payload...")
            val payload = String(Base64.getUrlDecoder().decode(parts[1]))
            val json = Json.parseToJsonElement(payload).jsonObject

            Log.d(TAG, "🔍 JWT payload: $payload")

            val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull()
            if (exp == null) {
                Log.e(TAG, "❌ 令牌格式无效: 无法解析过期时间")
                return false
            }

            val expirationTime = exp * 1000 // 转换为毫秒
            val currentTime = System.currentTimeMillis()

            // 只检查是否过期，不考虑刷新阈值
            val isValid = expirationTime > currentTime

            Log.i(TAG, "🔍 令牌有效性检查结果:")
            Log.i(TAG, "  - 过期时间: ${Date(expirationTime)}")
            Log.i(TAG, "  - 当前时间: ${Date(currentTime)}")
            Log.i(TAG, "  - 是否有效: $isValid")
            Log.i(TAG, "  - 剩余时间: ${(expirationTime - currentTime) / 1000 / 60} 分钟")

            isValid
        } catch (e: Exception) {
            Log.e(TAG, "❌ 令牌验证失败: ${e.message}", e)
            false
        }
    }

    /**
     * 检查令牌是否需要刷新（即将过期但还未过期）
     * 使用存储的过期时间，与iOS版本逻辑一致
     */
    private suspend fun shouldRefreshToken(token: String): Boolean {
        return try {
            // 优先使用存储的过期时间
            val expiresAt = userPreferences.getTokenExpiresAt().first()
            if (expiresAt != null) {
                val currentTime = System.currentTimeMillis()
                val refreshThreshold = 10 * 60 * 1000L // 10分钟，与iOS版本一致
                val shouldRefresh = expiresAt <= currentTime + refreshThreshold

                Log.d(TAG, "🔄 令牌刷新检查: 过期时间=${Date(expiresAt)}, 当前时间=${Date(currentTime)}, 需要刷新=$shouldRefresh")

                return shouldRefresh
            }

            // 如果没有存储的过期时间，回退到JWT解析
            Log.w(TAG, "⚠️ 没有存储的过期时间，回退到JWT解析")
            val parts = token.split(".")
            if (parts.size != 3) return true

            val payload = String(Base64.getUrlDecoder().decode(parts[1]))
            val json = Json.parseToJsonElement(payload).jsonObject
            val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull() ?: return true

            val expirationTime = exp * 1000 // 转换为毫秒
            val currentTime = System.currentTimeMillis()

            // 如果令牌在10分钟内过期，就需要刷新
            val refreshThreshold = 10 * 60 * 1000L // 10分钟
            val shouldRefresh = expirationTime <= currentTime + refreshThreshold

            Log.d(TAG, "🔄 令牌刷新检查(JWT): 过期时间=${Date(expirationTime)}, 需要刷新=$shouldRefresh")

            shouldRefresh
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 令牌检查失败，认为需要刷新", e)
            true // 检查失败时认为需要刷新
        }
    }

    /**
     * 开始OIDC标准登录流程
     * 按照OpenID Connect Core 1.0规范实现
     */
    suspend fun startOidcLogin(): String {
        Log.i(TAG, "🎯 开始OIDC标准登录流程")
        updateAuthState(isLoading = true)

        return try {
            // 生成PKCE参数
            val pkceState = oidcTokenService.generatePkceParameters()

            // 保存PKCE状态到本地存储
            userPreferences.saveAuthState(pkceState.state)
            userPreferences.saveCodeVerifier(pkceState.codeVerifier)

            // 构建授权URL
            val authUrl = oidcTokenService.buildAuthorizationUrl(pkceState)
            Log.i(TAG, "🔗 生成OIDC授权URL: ${authUrl.take(100)}...")

            authUrl
        } catch (e: Exception) {
            Log.e(TAG, "❌ 生成OIDC授权URL失败", e)
            updateAuthState(isLoading = false, error = e.message)
            throw e
        }
    }

    /**
     * 开始OAuth2.0登录流程（保留兼容性）
     */
    suspend fun startLogin(): String {
        Log.i(TAG, "🎯 开始OAuth2.0登录流程（重定向到OIDC标准流程）")
        return startOidcLogin()
    }

    /**
     * 构建授权URL
     */
    private fun buildAuthorizationUrl(): String {
        val state = java.util.UUID.randomUUID().toString()
        val codeVerifier = generateCodeVerifier()
        val codeChallenge = generateCodeChallenge(codeVerifier)

        // 保存状态和code verifier
        kotlinx.coroutines.runBlocking {
            userPreferences.saveAuthState(state)
            userPreferences.saveCodeVerifier(codeVerifier)
        }

        return "${LogtoConfig.LOGTO_ENDPOINT}/oidc/auth?" +
                "client_id=${LogtoConfig.CLIENT_ID}&" +
                "redirect_uri=${LogtoConfig.REDIRECT_URI}&" +
                "response_type=code&" +
                "scope=openid profile email offline_access&" +
                "state=$state&" +
                "code_challenge=$codeChallenge&" +
                "code_challenge_method=S256&" +
                "prompt=consent"
    }

    /**
     * 处理OIDC授权回调
     * 按照OpenID Connect Core 1.0 Section 3.1.2.5和3.1.3实现
     */
    suspend fun handleOidcCallback(code: String, state: String): Result<Unit> {
        Log.i(TAG, "🔄 处理OIDC授权回调")
        updateAuthState(isLoading = true)

        return try {
            // 验证state (CSRF保护)
            val savedState = userPreferences.getAuthState().first()
            if (state != savedState) {
                Log.e(TAG, "❌ State参数不匹配，可能是CSRF攻击")
                throw SecurityException("State参数不匹配，可能是CSRF攻击")
            }

            // 获取之前保存的code_verifier
            val codeVerifier = userPreferences.getCodeVerifier().first()
            if (codeVerifier.isNullOrEmpty()) {
                Log.e(TAG, "❌ 找不到code_verifier，无法完成OIDC流程")
                throw IllegalStateException("找不到code_verifier，无法完成OIDC流程")
            }

            // 使用授权码交换令牌 (OpenID Connect Core 1.0 Section 3.1.3)
            Log.i(TAG, "🔄 使用授权码交换令牌...")
            val tokenResult = oidcTokenService.exchangeCodeForTokens(code, codeVerifier, state)

            if (tokenResult.isSuccess) {
                val tokenResponse = tokenResult.getOrThrow()
                Log.i(TAG, "✅ 令牌交换成功")

                // 保存令牌信息
                userPreferences.saveAccessToken(tokenResponse.accessToken)
                tokenResponse.refreshToken?.let {
                    Log.i(TAG, "💾 保存刷新令牌: ${it.take(20)}...")
                    userPreferences.saveRefreshToken(it)
                }
                tokenResponse.idToken?.let {
                    Log.i(TAG, "💾 保存ID令牌")
                    userPreferences.saveIdToken(it)
                }

                // 计算并保存令牌过期时间
                tokenResponse.expiresIn?.let { expiresIn ->
                    val expiresAt = System.currentTimeMillis() + (expiresIn * 1000)
                    Log.i(TAG, "⏱️ 保存令牌过期时间: ${Date(expiresAt)}")
                    userPreferences.saveTokenExpiresAt(expiresAt)
                }

                // 获取用户信息 (OpenID Connect Core 1.0 Section 5.3)
                Log.i(TAG, "👤 获取OIDC用户信息...")
                val userInfoResult = oidcUserInfoService.getUserInfo(tokenResponse.accessToken)

                if (userInfoResult.isSuccess) {
                    val userInfo = userInfoResult.getOrThrow()
                    Log.i(TAG, "✅ 获取用户信息成功")

                    // 保存Logto ID (sub)
                    userPreferences.saveLogtoId(userInfo.sub)

                    // 调用后端API获取user_id
                    Log.i(TAG, "🔄 调用后端用户信息API获取user_id...")
                    val backendUserResult = getUserProfile()

                    when (backendUserResult) {
                        is NetworkResult.Success -> {
                            val backendUser = backendUserResult.data
                            Log.i(TAG, "✅ 获取到后端user_id: ${backendUser.userId}")

                            // 保存用户信息
                            userPreferences.saveUserId(backendUser.userId)
                            userPreferences.saveUserInfo(
                                backendUser.userId,
                                backendUser.email,
                                backendUser.nickname ?: backendUser.username
                            )

                            // 创建User对象用于状态更新
                            val user = User(
                                userId = backendUser.userId,
                                username = backendUser.username,
                                email = backendUser.email,
                                phone = "",
                                nickname = backendUser.nickname ?: "",
                                status = 1,
                                createdAt = "",
                                updatedAt = ""
                            )

                            // 更新认证状态（通过saveUserInfo已经设置了IS_AUTHENTICATED_KEY = true）
                            updateAuthState(isAuthenticated = true, user = user, isLoading = false)
                            Log.i(TAG, "✅ OIDC登录成功，user_id: ${backendUser.userId}")

                            Result.success(Unit)
                        }
                        is NetworkResult.Error -> {
                            Log.w(TAG, "⚠️ 获取后端用户信息失败，使用Logto信息作为备用", backendUserResult.exception)

                            // 使用Logto用户信息作为备用
                            val displayName = oidcUserInfoService.extractDisplayName(userInfo)
                            userPreferences.saveUserId(userInfo.sub)
                            userPreferences.saveUserInfo(
                                userInfo.sub,
                                userInfo.email ?: "",
                                displayName
                            )

                            // 创建User对象用于状态更新
                            val user = User(
                                userId = userInfo.sub,
                                username = userInfo.preferredUsername ?: displayName,
                                email = userInfo.email ?: "",
                                phone = userInfo.phoneNumber ?: "",
                                nickname = userInfo.name ?: "",
                                status = 1,
                                createdAt = "",
                                updatedAt = ""
                            )

                            // 更新认证状态（通过saveUserInfo已经设置了IS_AUTHENTICATED_KEY = true）
                            updateAuthState(isAuthenticated = true, user = user, isLoading = false)
                            Log.i(TAG, "✅ OIDC登录成功（使用Logto信息），sub: ${userInfo.sub}")

                            Result.success(Unit)
                        }
                        else -> {
                            Log.w(TAG, "⚠️ 获取后端用户信息返回未知结果，使用Logto信息")

                            // 使用Logto用户信息
                            val displayName = oidcUserInfoService.extractDisplayName(userInfo)
                            userPreferences.saveUserId(userInfo.sub)
                            userPreferences.saveUserInfo(
                                userInfo.sub,
                                userInfo.email ?: "",
                                displayName
                            )

                            // 更新认证状态（通过saveUserInfo已经设置了IS_AUTHENTICATED_KEY = true）
                            updateAuthState(isAuthenticated = true, isLoading = false)
                            Log.i(TAG, "✅ OIDC登录成功（使用Logto信息），sub: ${userInfo.sub}")

                            Result.success(Unit)
                        }
                    }
                } else {
                    val exception = userInfoResult.exceptionOrNull() ?: Exception("获取用户信息失败")
                    Log.e(TAG, "❌ 获取用户信息失败", exception)
                    clearAuthCredentials()
                    updateAuthState(isAuthenticated = false, isLoading = false, error = exception.message)
                    Result.failure(exception)
                }
            } else {
                val exception = tokenResult.exceptionOrNull() ?: Exception("令牌交换失败")
                Log.e(TAG, "❌ 令牌交换失败", exception)
                clearAuthCredentials()
                updateAuthState(isAuthenticated = false, isLoading = false, error = exception.message)
                Result.failure(exception)
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 处理OIDC授权回调失败", e)
            clearAuthCredentials()
            updateAuthState(isAuthenticated = false, isLoading = false, error = e.message)
            Result.failure(e)
        }
    }

    /**
     * 处理授权回调（保留兼容性）
     */
    suspend fun handleAuthCallback(code: String, state: String): NetworkResult<Unit> {
        Log.i(TAG, "🔄 处理授权回调（重定向到OIDC标准流程）")

        val result = handleOidcCallback(code, state)

        return if (result.isSuccess) {
            NetworkResult.Success(Unit)
        } else {
            val exception = result.exceptionOrNull() ?: Exception("处理授权回调失败")
            NetworkResult.Error(exception)
        }
    }

    /**
     * 检查当前令牌是否需要刷新（公共方法）
     * 使用Logto SDK时，这个方法简化为总是返回false，因为SDK会自动处理刷新
     */
    suspend fun isTokenRefreshNeeded(): Boolean {
        return try {
            Log.d(TAG, "🔍 使用Logto SDK，无需手动检查令牌刷新")
            false
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 检查令牌刷新需求失败: ${e.message}")
            false
        }
    }

    /**
     * 刷新访问令牌 - 使用OIDC标准流程
     * 按照OpenID Connect Core 1.0 Section 12实现
     */
    suspend fun refreshToken(): String {
        Log.i(TAG, "🔄 使用OIDC标准流程刷新令牌")

        try {
            // 获取存储的刷新令牌
            val refreshToken = userPreferences.getRefreshToken().first()
            if (refreshToken.isNullOrEmpty()) {
                Log.e(TAG, "❌ 找不到刷新令牌，需要重新登录")
                throw IllegalStateException("找不到刷新令牌，需要重新登录")
            }

            Log.d(TAG, "🔄 使用刷新令牌: ${refreshToken.take(20)}...")

            // 使用OIDC标准刷新流程
            val tokenResult = oidcTokenService.refreshAccessToken(refreshToken)

            if (tokenResult.isSuccess) {
                val tokenResponse = tokenResult.getOrThrow()
                Log.i(TAG, "✅ OIDC令牌刷新成功")

                // 保存新的访问令牌
                userPreferences.saveAccessToken(tokenResponse.accessToken)

                // 如果返回了新的刷新令牌，也要保存
                tokenResponse.refreshToken?.let { newRefreshToken ->
                    Log.i(TAG, "💾 保存新的刷新令牌: ${newRefreshToken.take(20)}...")
                    userPreferences.saveRefreshToken(newRefreshToken)
                }

                // 更新令牌过期时间
                tokenResponse.expiresIn?.let { expiresIn ->
                    val expiresAt = System.currentTimeMillis() + (expiresIn * 1000)
                    Log.i(TAG, "⏱️ 更新令牌过期时间: ${Date(expiresAt)}")
                    userPreferences.saveTokenExpiresAt(expiresAt)
                }

                Log.i(TAG, "✅ 令牌刷新成功，返回新的访问令牌: ${tokenResponse.accessToken.take(20)}...")
                return tokenResponse.accessToken
            } else {
                val exception = tokenResult.exceptionOrNull() ?: Exception("令牌刷新失败")
                Log.e(TAG, "❌ OIDC令牌刷新失败", exception)

                // 刷新失败，清除认证信息
                clearAuthCredentials()
                throw exception
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 令牌刷新失败: ${e.message}", e)
            // 刷新失败，清除认证信息
            clearAuthCredentials()
            throw e
        }
    }

    /**
     * 使用Logto SDK刷新令牌（备用方法）
     */
    suspend fun refreshTokenWithSdk(): String {
        Log.i(TAG, "🔄 使用Logto SDK刷新令牌（备用方法）")

        try {
            // 直接使用LogtoManager获取访问令牌，SDK会自动处理刷新
            val newToken = logtoManager.getAccessToken()

            if (newToken.isNullOrEmpty()) {
                Log.e(TAG, "❌ Logto SDK获取令牌失败")
                throw IllegalStateException("Logto SDK token refresh failed")
            }

            Log.i(TAG, "✅ Logto SDK令牌获取成功: ${newToken.take(20)}...")

            // 保存新令牌到本地存储（用于缓存）
            userPreferences.saveAccessToken(newToken)

            return newToken
        } catch (e: Exception) {
            Log.e(TAG, "❌ Logto SDK令牌刷新失败: ${e.message}", e)
            // 刷新失败，清除认证信息
            clearAuthCredentials()
            throw e
        }
    }

    /**
     * 处理OAuth回调，获取后端user_id
     */
    private suspend fun handleOAuthCallback(code: String, state: String): NetworkResult<OAuthCallbackResponse> {
        return safeApiCall {
            apiService.handleOAuthCallback(code, state)
        }
    }

    /**
     * 获取用户资料
     */
    private suspend fun getUserProfile(): NetworkResult<UserInfoResponse> {
        return safeApiCall {
            apiService.getUserProfile()
        }
    }

    /**
     * 加载用户资料
     */
    private suspend fun loadUserProfile() {
        when (val result = getUserProfile()) {
            is NetworkResult.Success -> {
                val userInfo = result.data
                // 将UserInfoResponse转换为User对象
                val user = User(
                    userId = userInfo.userId,
                    username = userInfo.username,
                    email = userInfo.email,
                    phone = "",
                    nickname = userInfo.nickname ?: "",
                    status = 1,
                    createdAt = "",
                    updatedAt = ""
                )
                updateAuthState(user = user)
            }
            is NetworkResult.Error -> {
                Log.e(TAG, "加载用户资料失败", result.exception)
            }
            else -> {}
        }
    }

    /**
     * 登出
     */
    suspend fun logout() {
        Log.i(TAG, "🚪 用户登出")
        clearAuthCredentials()
        updateAuthState(isAuthenticated = false, user = null)
    }

    /**
     * 清除认证凭据
     */
    private suspend fun clearAuthCredentials() {
        userPreferences.clearAuthCredentials()
    }

    /**
     * 更新认证状态
     */
    private fun updateAuthState(
        isAuthenticated: Boolean? = null,
        isLoading: Boolean? = null,
        error: String? = null,
        user: User? = null
    ) {
        _authState.value = _authState.value.copy(
            isAuthenticated = isAuthenticated ?: _authState.value.isAuthenticated,
            isLoading = isLoading ?: _authState.value.isLoading,
            error = error,
            user = user ?: _authState.value.user
        )
    }

    /**
     * 刷新认证状态
     */
    suspend fun refreshAuthState() {
        val isAuthenticated = userPreferences.isAuthenticated().first()
        updateAuthState(isAuthenticated = isAuthenticated)
        if (isAuthenticated) {
            loadUserProfile()
        }
    }

    /**
     * 检查应用启动时的令牌状态
     * 用于应用启动时快速检查令牌是否有效
     */
    suspend fun checkStartupTokenStatus(): Boolean {
        return try {
            val isAuthenticated = userPreferences.isAuthenticated().first()
            val accessToken = userPreferences.getAccessToken().first()

            if (isAuthenticated && !accessToken.isNullOrEmpty()) {
                // 检查令牌是否有效
                if (isTokenValid(accessToken)) {
                    Log.i(TAG, "🚀 启动检查：令牌有效，用户已登录")
                    return true
                } else {
                    Log.w(TAG, "🚀 启动检查：令牌无效或过期")
                    return false
                }
            } else {
                Log.i(TAG, "🚀 启动检查：用户未登录")
                return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "🚀 启动检查失败: ${e.message}")
            false
        }
    }

    // MARK: - Helper Methods

    private fun generateCodeVerifier(): String {
        val bytes = ByteArray(32)
        java.security.SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }

    private fun generateCodeChallenge(codeVerifier: String): String {
        val digest = java.security.MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(codeVerifier.toByteArray())
        return Base64.getUrlEncoder().withoutPadding().encodeToString(hash)
    }

    /**
     * 原生Logto登录方法 - 使用Logto SDK内部令牌管理
     */
    suspend fun signInNative(activity: android.app.Activity): Result<Unit> {
        return try {
            Log.i(TAG, "🚀 开始原生Logto登录（使用SDK内部令牌管理）")
            updateAuthState(isLoading = true, error = null)

            // 验证配置
            if (!com.cabycare.android.debug.LogtoDebugHelper.checkConfiguration()) {
                val error = "Logto配置验证失败"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                return Result.failure(Exception(error))
            }

            Log.i(TAG, "📱 调用Logto SDK登录")
            logtoManager.signIn(activity)

            Log.i(TAG, "🔑 获取有效访问令牌（SDK会自动处理刷新）")
            // 使用getAccessToken获取有效的令牌（SDK会自动处理刷新）
            val accessToken = logtoManager.getAccessToken()
            val idToken = logtoManager.getUserInfo()?.get("sub") as? String

            Log.d(TAG, "访问令牌: ${if (accessToken != null) "已获取" else "未获取"}")
            Log.d(TAG, "ID令牌: ${if (idToken != null) "已获取" else "未获取"}")
            Log.d(TAG, "刷新令牌: 由SDK内部管理")

            if (accessToken != null) {
                Log.i(TAG, "💾 保存令牌到本地存储")
                // 保存令牌到本地存储（refresh token由SDK管理，无需手动保存）
                userPreferences.saveAccessToken(accessToken)
                idToken?.let { userPreferences.saveIdToken(it) }

                Log.i(TAG, "👤 获取Logto用户信息")
                // 获取Logto用户信息
                val userInfo = logtoManager.getUserInfo()
                userInfo?.let { info ->
                    Log.d(TAG, "Logto用户信息: $info")
                    val logtoId = info["sub"] as? String // Logto认证服务的用户ID
                    val email = info["email"] as? String
                    val name = info["name"] as? String

                    logtoId?.let {
                        userPreferences.saveLogtoId(it)
                        Log.i(TAG, "✅ 保存Logto ID: $it")

                        // 🔑 关键修复：调用后端API获取user_id
                        Log.i(TAG, "🔄 调用后端用户信息API获取user_id...")
                        try {
                            // 调用 /api/user/info 获取用户信息，包含正确的user_id
                            val userInfoResult = getUserProfile()
                            when (userInfoResult) {
                                is NetworkResult.Success -> {
                                    val userInfo = userInfoResult.data
                                    Log.i(TAG, "✅ 获取到后端user_id: ${userInfo.userId}")

                                    // 保存正确的user_id和用户信息
                                    userPreferences.saveUserInfo(userInfo.userId, userInfo.email, userInfo.nickname ?: userInfo.username)
                                }
                                is NetworkResult.Error -> {
                                    Log.e(TAG, "❌ 获取用户信息失败: ${userInfoResult.exception.message}")
                                    // 如果获取用户信息失败，我们暂时使用logto_id作为fallback
                                    userPreferences.saveUserId(logtoId)
                                    userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")
                                }
                                else -> {
                                    Log.w(TAG, "⚠️ 获取用户信息返回未知结果")
                                    userPreferences.saveUserId(logtoId)
                                    userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "❌ 获取用户信息异常: ${e.message}")
                            // 异常情况下使用logto_id作为fallback
                            userPreferences.saveUserId(logtoId)
                            userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")
                        }
                    }
                }

                updateAuthState(isAuthenticated = true, isLoading = false)
                Log.i(TAG, "✅ 原生登录成功")
                Result.success(Unit)
            } else {
                val error = "无法获取访问令牌"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                Result.failure(Exception(error))
            }
        } catch (e: Exception) {
            val errorMsg = "原生登录失败: ${e.message}"
            Log.e(TAG, "❌ $errorMsg", e)
            updateAuthState(isAuthenticated = false, isLoading = false, error = e.message)
            Result.failure(e)
        }
    }

    /**
     * 完整OIDC登录流程
     * 按照OpenID Connect Core 1.0规范实现
     *
     * 这个方法提供了一个完整的OIDC登录流程，包括：
     * 1. 验证OIDC配置
     * 2. 生成PKCE参数
     * 3. 构建授权URL
     * 4. 处理授权回调
     * 5. 交换令牌
     * 6. 获取用户信息
     * 7. 保存认证状态
     *
     * @param activity 当前活动
     * @return 授权URL，用于重定向用户到Logto登录页面
     */
    suspend fun startOidcSignIn(activity: android.app.Activity): Result<String> {
        return try {
            Log.i(TAG, "🚀 开始完整OIDC登录流程")
            updateAuthState(isLoading = true, error = null)

            // 1. 验证OIDC配置
            Log.i(TAG, "🔍 验证OIDC配置")
            val isConfigValid = logtoManager.validateOidcConfiguration()
            if (!isConfigValid) {
                val error = "OIDC配置验证失败"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                return Result.failure(Exception(error))
            }

            // 2. 生成PKCE参数并构建授权URL
            Log.i(TAG, "🔐 生成PKCE参数并构建授权URL")
            val authUrl = startOidcLogin()

            Log.i(TAG, "✅ OIDC登录流程已启动，返回授权URL")
            Result.success(authUrl)
        } catch (e: Exception) {
            val errorMsg = "OIDC登录流程启动失败: ${e.message}"
            Log.e(TAG, "❌ $errorMsg", e)
            updateAuthState(isAuthenticated = false, isLoading = false, error = e.message)
            Result.failure(e)
        }
    }

    /**
     * 登出方法
     */
    suspend fun signOut(): Result<Unit> {
        return try {
            updateAuthState(isLoading = true)

            // 使用Logto SDK登出
            logtoManager.signOut()

            // 清除本地存储的认证信息
            clearAuthCredentials()

            Log.i(TAG, "✅ 登出成功")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 登出失败", e)
            // 即使登出失败，也清除本地认证信息
            clearAuthCredentials()
            Result.failure(e)
        }
    }
}

/**
 * 令牌响应模型
 */
@kotlinx.serialization.Serializable
data class TokenResponse(
    @kotlinx.serialization.SerialName("access_token")
    val accessToken: String,
    @kotlinx.serialization.SerialName("refresh_token")
    val refreshToken: String? = null,
    @kotlinx.serialization.SerialName("token_type")
    val tokenType: String = "Bearer",
    @kotlinx.serialization.SerialName("expires_in")
    val expiresIn: Long? = null
)
