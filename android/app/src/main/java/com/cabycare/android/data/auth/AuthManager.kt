package com.cabycare.android.data.auth

import android.content.Context
import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.model.OAuthCallbackResponse
import com.cabycare.android.data.model.TokenRefreshResponse
import com.cabycare.android.data.model.User
import com.cabycare.android.data.model.UserInfoResponse
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.network.safeApiCall
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.Base64
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton
import com.cabycare.android.di.BasicOkHttpClient
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.long

/**
 * 认证状态数据类
 */
data class AuthState(
    val isAuthenticated: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null,
    val user: User? = null,
    val accessToken: String? = null,
    val refreshToken: String? = null,
    val idToken: String? = null,
    val tokenExpiresAt: Long? = null
)

/**
 * 认证管理器
 * 负责用户认证、令牌管理和自动刷新
 */
@Singleton
class AuthManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userPreferences: UserPreferences,
    private val apiService: ApiService,
    private val logtoManager: LogtoManager
) {
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    companion object {
        private const val TAG = "AuthManager"
        private const val TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000L // 5分钟
    }
    
    private val _authState = MutableStateFlow(AuthState())
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    private val refreshMutex = Mutex()
    private var refreshTask: kotlinx.coroutines.Deferred<String>? = null
    
    init {
        // 调试信息
        com.cabycare.android.debug.LogtoDebugHelper.printConfig()
        com.cabycare.android.debug.LogtoDebugHelper.checkConfiguration()

        // 初始化时检查认证状态
        checkInitialAuthStatus()

        // 添加额外的调试信息
        scope.launch {
            Log.i(TAG, "🔍 AuthManager初始化完成，开始详细检查...")

            try {
                val isAuth = userPreferences.isAuthenticated().first()
                val token = userPreferences.getAccessToken().first()
                val refreshToken = userPreferences.getRefreshToken().first()
                val expiresAt = userPreferences.getTokenExpiresAt().first()

                Log.i(TAG, "🔍 详细检查结果:")
                Log.i(TAG, "  - isAuthenticated: $isAuth")
                Log.i(TAG, "  - hasAccessToken: ${!token.isNullOrEmpty()}")
                Log.i(TAG, "  - hasRefreshToken: ${!refreshToken.isNullOrEmpty()}")
                Log.i(TAG, "  - tokenExpiresAt: $expiresAt")

                if (token != null && token.isNotEmpty()) {
                    Log.i(TAG, "  - accessToken前缀: ${token.take(20)}...")
                    Log.i(TAG, "  - accessToken长度: ${token.length}")
                }

                if (expiresAt != null) {
                    val currentTime = System.currentTimeMillis()
                    val isExpired = expiresAt <= currentTime
                    Log.i(TAG, "  - 过期时间: ${java.util.Date(expiresAt)}")
                    Log.i(TAG, "  - 当前时间: ${java.util.Date(currentTime)}")
                    Log.i(TAG, "  - 是否已过期: $isExpired")
                }

            } catch (e: Exception) {
                Log.e(TAG, "🔍 详细检查失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 检查初始认证状态
     * 应用启动时立即检查令牌状态，如果令牌有效则直接进入主界面
     */
    private fun checkInitialAuthStatus() {
        // 先将认证状态设置为加载中
        updateAuthState(isLoading = true)

        scope.launch {
            Log.i(TAG, "🔍 开始检查初始认证状态")

            // 打印存储的认证数据用于调试
            userPreferences.printAuthData()

            val isAuthenticated = userPreferences.isAuthenticated().first()
            val accessToken = userPreferences.getAccessToken().first()
            val refreshToken = userPreferences.getRefreshToken().first()

            Log.d(TAG, "🔍 存储状态检查:")
            Log.d(TAG, "  - isAuthenticated: $isAuthenticated")
            Log.d(TAG, "  - accessToken存在: ${!accessToken.isNullOrEmpty()}")
            Log.d(TAG, "  - refreshToken存在: ${!refreshToken.isNullOrEmpty()}")

            if (accessToken != null) {
                Log.d(TAG, "  - accessToken前缀: ${accessToken.take(20)}...")
            }
            if (refreshToken != null) {
                Log.d(TAG, "  - refreshToken前缀: ${refreshToken.take(20)}...")
            }

            if (isAuthenticated && !accessToken.isNullOrEmpty()) {
                Log.i(TAG, "🔍 检查存储的访问令牌状态")

                // 检查令牌格式和有效性
                if (accessToken.length < 100) {
                    Log.w(TAG, "⚠️ 访问令牌长度异常（${accessToken.length}），可能不是标准JWT格式")
                    Log.w(TAG, "⚠️ 这可能是Logto SDK的内部令牌，需要重新登录获取OIDC令牌")
                    clearAuthCredentials()
                    updateAuthState(isAuthenticated = false, isLoading = false)
                } else if (isTokenValid(accessToken)) {
                    Log.i(TAG, "🔑 发现有效的访问令牌，用户已登录")
                    updateAuthState(isAuthenticated = true, isLoading = false)
                    loadUserProfile()
                } else {
                    Log.w(TAG, "⚠️ 访问令牌无效，使用Logto SDK尝试刷新")
                    try {
                        val newToken = refreshToken()
                        Log.i(TAG, "✅ 令牌刷新成功，用户保持登录状态")
                        updateAuthState(isAuthenticated = true, isLoading = false)
                        loadUserProfile()
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 令牌刷新失败，需要重新登录", e)
                        clearAuthCredentials()
                        updateAuthState(isAuthenticated = false, isLoading = false)
                    }
                }
            } else {
                if (!isAuthenticated) {
                    Log.i(TAG, "🔒 用户未登录 - isAuthenticated为false")
                } else if (accessToken.isNullOrEmpty()) {
                    Log.i(TAG, "🔒 用户未登录 - accessToken为空")
                }

                Log.w(TAG, "⚠️ 没有找到有效的认证数据")
                Log.w(TAG, "⚠️ 可能的原因:")
                Log.w(TAG, "  1. 用户从未登录过")
                Log.w(TAG, "  2. 应用数据被清除")
                Log.w(TAG, "  3. 令牌存储失败")
                Log.w(TAG, "  4. DataStore读取失败")

                updateAuthState(isAuthenticated = false, isLoading = false)
            }
        }
    }
    
    /**
     * 检查令牌是否有效（不考虑刷新阈值，只检查是否过期）
     */
    private fun isTokenValid(token: String): Boolean {
        Log.i(TAG, "🔍 开始检查令牌有效性...")

        return try {
            val parts = token.split(".")
            if (parts.size != 3) {
                Log.e(TAG, "❌ 令牌格式无效: 不是有效的JWT格式（应有3部分）")
                return false
            }

            Log.d(TAG, "🔍 JWT格式有效，解析payload...")
            val payload = String(Base64.getUrlDecoder().decode(parts[1]))
            val json = Json.parseToJsonElement(payload).jsonObject

            Log.d(TAG, "🔍 JWT payload: $payload")

            val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull()
            if (exp == null) {
                Log.e(TAG, "❌ 令牌格式无效: 无法解析过期时间")
                return false
            }

            val expirationTime = exp * 1000 // 转换为毫秒
            val currentTime = System.currentTimeMillis()

            // 只检查是否过期，不考虑刷新阈值
            val isValid = expirationTime > currentTime

            Log.i(TAG, "🔍 令牌有效性检查结果:")
            Log.i(TAG, "  - 过期时间: ${Date(expirationTime)}")
            Log.i(TAG, "  - 当前时间: ${Date(currentTime)}")
            Log.i(TAG, "  - 是否有效: $isValid")
            Log.i(TAG, "  - 剩余时间: ${(expirationTime - currentTime) / 1000 / 60} 分钟")

            isValid
        } catch (e: Exception) {
            Log.e(TAG, "❌ 令牌验证失败: ${e.message}", e)
            false
        }
    }







    /**
     * 检查当前令牌是否需要刷新（公共方法）
     * 使用Logto SDK时，这个方法简化为总是返回false，因为SDK会自动处理刷新
     */
    suspend fun isTokenRefreshNeeded(): Boolean {
        return try {
            Log.d(TAG, "🔍 使用Logto SDK，无需手动检查令牌刷新")
            false
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 检查令牌刷新需求失败: ${e.message}")
            false
        }
    }

    /**
     * 刷新访问令牌 - 使用Logto SDK
     */
    suspend fun refreshToken(): String {
        Log.i(TAG, "🔄 使用Logto SDK刷新令牌")

        try {
            // 直接使用LogtoManager的refreshToken方法，SDK会自动处理刷新
            val newToken = logtoManager.refreshToken()

            if (newToken.isNullOrEmpty()) {
                Log.e(TAG, "❌ Logto SDK令牌刷新失败")
                throw IllegalStateException("Logto SDK token refresh failed")
            }

            Log.i(TAG, "✅ Logto SDK令牌刷新成功: ${newToken.take(20)}...")

            // 保存新令牌到本地存储（用于缓存）
            userPreferences.saveAccessToken(newToken)

            return newToken
        } catch (e: Exception) {
            Log.e(TAG, "❌ Logto SDK令牌刷新失败: ${e.message}", e)
            // 刷新失败，清除认证信息
            clearAuthCredentials()
            throw e
        }
    }

    /**
     * 处理OAuth回调，获取后端user_id
     */
    private suspend fun handleOAuthCallback(code: String, state: String): NetworkResult<OAuthCallbackResponse> {
        return safeApiCall {
            apiService.handleOAuthCallback(code, state)
        }
    }

    /**
     * 获取用户资料
     */
    private suspend fun getUserProfile(): NetworkResult<UserInfoResponse> {
        return safeApiCall {
            apiService.getUserProfile()
        }
    }

    /**
     * 加载用户资料
     */
    private suspend fun loadUserProfile() {
        when (val result = getUserProfile()) {
            is NetworkResult.Success -> {
                val userInfo = result.data
                // 将UserInfoResponse转换为User对象
                val user = User(
                    userId = userInfo.userId,
                    username = userInfo.username,
                    email = userInfo.email,
                    phone = "",
                    nickname = userInfo.nickname ?: "",
                    status = 1,
                    createdAt = "",
                    updatedAt = ""
                )
                updateAuthState(user = user)
            }
            is NetworkResult.Error -> {
                Log.e(TAG, "加载用户资料失败", result.exception)
            }
            else -> {}
        }
    }

    /**
     * 登出
     */
    suspend fun logout() {
        Log.i(TAG, "🚪 用户登出")
        clearAuthCredentials()
        updateAuthState(isAuthenticated = false, user = null)
    }

    /**
     * 清除认证凭据
     */
    private suspend fun clearAuthCredentials() {
        userPreferences.clearAuthCredentials()
    }

    /**
     * 更新认证状态
     */
    private fun updateAuthState(
        isAuthenticated: Boolean? = null,
        isLoading: Boolean? = null,
        error: String? = null,
        user: User? = null,
        accessToken: String? = null,
        refreshToken: String? = null,
        idToken: String? = null,
        tokenExpiresAt: Long? = null
    ) {
        val oldState = _authState.value
        val newState = oldState.copy(
            isAuthenticated = isAuthenticated ?: oldState.isAuthenticated,
            isLoading = isLoading ?: oldState.isLoading,
            error = error,
            user = user ?: oldState.user,
            accessToken = accessToken ?: oldState.accessToken,
            refreshToken = refreshToken ?: oldState.refreshToken,
            idToken = idToken ?: oldState.idToken,
            tokenExpiresAt = tokenExpiresAt ?: oldState.tokenExpiresAt
        )

        _authState.value = newState

        // 添加详细的状态更新日志
        Log.d(TAG, "🔄 AuthState更新:")
        Log.d(TAG, "  - isAuthenticated: ${oldState.isAuthenticated} → ${newState.isAuthenticated}")
        Log.d(TAG, "  - isLoading: ${oldState.isLoading} → ${newState.isLoading}")
        Log.d(TAG, "  - error: ${oldState.error} → ${newState.error}")
        Log.d(TAG, "  - user: ${oldState.user?.userId ?: "null"} → ${newState.user?.userId ?: "null"}")
        Log.d(TAG, "  - accessToken: ${oldState.accessToken?.take(20) ?: "null"} → ${newState.accessToken?.take(20) ?: "null"}")
        Log.d(TAG, "  - refreshToken: ${if (oldState.refreshToken != null) "存在" else "null"} → ${if (newState.refreshToken != null) "存在" else "null"}")
        Log.d(TAG, "  - idToken: ${if (oldState.idToken != null) "存在" else "null"} → ${if (newState.idToken != null) "存在" else "null"}")
        Log.d(TAG, "  - tokenExpiresAt: ${oldState.tokenExpiresAt} → ${newState.tokenExpiresAt}")

        if (newState.user != null) {
            Log.d(TAG, "  - 用户详情: userId=${newState.user.userId}, email=${newState.user.email}, username=${newState.user.username}")
        }
    }

    /**
     * 获取当前有效的访问令牌
     * 优先使用AuthState中的令牌，如果没有则从Logto SDK获取
     */
    suspend fun getCurrentAccessToken(): String? {
        return try {
            // 首先尝试从AuthState获取
            val authStateToken = _authState.value.accessToken
            if (!authStateToken.isNullOrEmpty()) {
                Log.d(TAG, "🔑 从AuthState获取访问令牌: ${authStateToken.take(20)}...")
                return authStateToken
            }

            // 如果AuthState中没有，尝试从Logto SDK获取
            Log.d(TAG, "🔍 AuthState中没有令牌，从Logto SDK获取")
            val sdkToken = logtoManager.getAccessToken()
            if (!sdkToken.isNullOrEmpty()) {
                Log.d(TAG, "🔑 从Logto SDK获取访问令牌: ${sdkToken.take(20)}...")
                // 更新AuthState中的令牌
                updateAuthState(accessToken = sdkToken)
                // 同时保存到本地存储
                userPreferences.saveAccessToken(sdkToken)
                return sdkToken
            }

            Log.w(TAG, "⚠️ 无法获取访问令牌")
            null
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取访问令牌失败: ${e.message}", e)
            null
        }
    }

    /**
     * 刷新认证状态
     */
    suspend fun refreshAuthState() {
        val isAuthenticated = userPreferences.isAuthenticated().first()
        updateAuthState(isAuthenticated = isAuthenticated)
        if (isAuthenticated) {
            loadUserProfile()
        }
    }

    /**
     * 检查应用启动时的令牌状态
     * 用于应用启动时快速检查令牌是否有效，支持自动刷新
     */
    suspend fun checkStartupTokenStatus(): Boolean {
        return try {
            Log.i(TAG, "🚀 启动检查：开始验证令牌状态")

            val isAuthenticated = userPreferences.isAuthenticated().first()
            val accessToken = userPreferences.getAccessToken().first()
            val refreshToken = userPreferences.getRefreshToken().first()

            Log.d(TAG, "🚀 启动检查状态:")
            Log.d(TAG, "  - isAuthenticated: $isAuthenticated")
            Log.d(TAG, "  - accessToken存在: ${!accessToken.isNullOrEmpty()}")
            Log.d(TAG, "  - refreshToken存在: ${!refreshToken.isNullOrEmpty()}")

            if (!isAuthenticated) {
                Log.i(TAG, "🚀 启动检查：用户未认证")
                return false
            }

            if (accessToken.isNullOrEmpty()) {
                Log.w(TAG, "🚀 启动检查：缺少访问令牌")
                return false
            }

            // 使用Logto SDK检查令牌有效性
            Log.d(TAG, "🚀 启动检查：使用Logto SDK验证令牌")
            val isTokenValid = try {
                // 尝试使用Logto SDK获取访问令牌，如果成功说明令牌有效
                val token = logtoManager.getAccessToken()
                !token.isNullOrEmpty()
            } catch (e: Exception) {
                Log.w(TAG, "🚀 启动检查：Logto SDK令牌验证失败: ${e.message}")
                false
            }

            if (isTokenValid) {
                Log.i(TAG, "🚀 启动检查：访问令牌有效，用户已登录")

                // 更新认证状态以确保UI同步
                try {
                    // 首先尝试从本地存储加载用户信息
                    Log.d(TAG, "🚀 启动检查：开始读取本地用户信息...")

                    val localUserId = try {
                        val id = userPreferences.getUserId().first()
                        Log.d(TAG, "  - 读取localUserId成功: $id")
                        id
                    } catch (e: Exception) {
                        Log.e(TAG, "  - 读取localUserId失败: ${e.message}", e)
                        null
                    }

                    val localUserEmail = try {
                        val email = userPreferences.getUserEmail().first()
                        Log.d(TAG, "  - 读取localUserEmail成功: $email")
                        email
                    } catch (e: Exception) {
                        Log.e(TAG, "  - 读取localUserEmail失败: ${e.message}", e)
                        null
                    }

                    val localUserName = try {
                        val name = userPreferences.getUserName().first()
                        Log.d(TAG, "  - 读取localUserName成功: $name")
                        name
                    } catch (e: Exception) {
                        Log.e(TAG, "  - 读取localUserName失败: ${e.message}", e)
                        null
                    }

                    Log.d(TAG, "🚀 启动检查：本地用户信息读取完成")
                    Log.d(TAG, "  - localUserId: $localUserId")
                    Log.d(TAG, "  - localUserEmail: $localUserEmail")
                    Log.d(TAG, "  - localUserName: $localUserName")

                    if (!localUserId.isNullOrEmpty()) {
                        // 使用本地用户信息创建User对象
                        val user = User(
                            userId = localUserId,
                            username = localUserName ?: "",
                            email = localUserEmail ?: "",
                            phone = "",
                            nickname = localUserName ?: "",
                            status = 1,
                            createdAt = "",
                            updatedAt = ""
                        )
                        updateAuthState(isAuthenticated = true, user = user, isLoading = false)
                        Log.i(TAG, "🚀 启动检查：本地用户信息已加载到应用状态")

                        // 可选：在后台刷新用户信息
                        try {
                            val userResult = getUserProfile()
                            if (userResult is NetworkResult.Success) {
                                val freshUserInfo = userResult.data
                                val freshUser = User(
                                    userId = freshUserInfo.userId,
                                    username = freshUserInfo.username,
                                    email = freshUserInfo.email,
                                    phone = "",
                                    nickname = freshUserInfo.nickname ?: "",
                                    status = 1,
                                    createdAt = "",
                                    updatedAt = ""
                                )
                                updateAuthState(isAuthenticated = true, user = freshUser, isLoading = false)
                                Log.d(TAG, "🚀 启动检查：用户信息已从服务器刷新")
                            }
                        } catch (e: Exception) {
                            Log.w(TAG, "🚀 启动检查：后台刷新用户信息失败: ${e.message}")
                            // 不影响主流程，继续使用本地信息
                        }
                    } else {
                        Log.w(TAG, "🚀 启动检查：本地用户ID为空，尝试从服务器获取")
                        // 本地用户信息不完整，尝试从服务器获取
                        val userResult = getUserProfile()
                        if (userResult is NetworkResult.Success) {
                            val userInfo = userResult.data
                            val user = User(
                                userId = userInfo.userId,
                                username = userInfo.username,
                                email = userInfo.email,
                                phone = "",
                                nickname = userInfo.nickname ?: "",
                                status = 1,
                                createdAt = "",
                                updatedAt = ""
                            )
                            updateAuthState(isAuthenticated = true, user = user, isLoading = false)
                            Log.i(TAG, "🚀 启动检查：用户信息已从服务器获取")
                        } else {
                            updateAuthState(isAuthenticated = true, isLoading = false)
                            Log.w(TAG, "🚀 启动检查：无法获取用户信息，仅更新认证状态")
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "🚀 启动检查：更新用户信息失败: ${e.message}", e)
                    updateAuthState(isAuthenticated = true, isLoading = false)
                }

                return true
            } else {
                Log.w(TAG, "🚀 启动检查：访问令牌无效或过期")

                // 尝试使用刷新令牌获取新的访问令牌
                if (!refreshToken.isNullOrEmpty()) {
                    Log.i(TAG, "🚀 启动检查：尝试使用刷新令牌获取新的访问令牌")
                    try {
                        val newAccessToken = refreshToken()
                        Log.i(TAG, "🚀 启动检查：令牌刷新成功，用户已登录")

                        // 更新认证状态
                        updateAuthState(isAuthenticated = true, isLoading = false)
                        return true
                    } catch (e: Exception) {
                        Log.e(TAG, "🚀 启动检查：令牌刷新失败: ${e.message}")
                        // 刷新失败，清除认证信息
                        clearAuthCredentials()
                        updateAuthState(isAuthenticated = false, isLoading = false)
                        return false
                    }
                } else {
                    Log.w(TAG, "🚀 启动检查：没有刷新令牌，无法自动刷新")
                    // 清除无效的认证信息
                    clearAuthCredentials()
                    updateAuthState(isAuthenticated = false, isLoading = false)
                    return false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "🚀 启动检查失败: ${e.message}", e)
            // 发生异常时清除认证状态
            updateAuthState(isAuthenticated = false, isLoading = false, error = e.message)
            return false
        }
    }

    /**
     * 快速检查令牌状态（不进行网络验证）
     * 用于快速启动场景
     */
    suspend fun quickStartupTokenCheck(): Boolean {
        return try {
            Log.d(TAG, "⚡ 快速启动检查：检查本地令牌状态")

            val isAuthenticated = userPreferences.isAuthenticated().first()
            val accessToken = userPreferences.getAccessToken().first()
            val tokenExpiresAt = userPreferences.getTokenExpiresAt().first()

            if (!isAuthenticated || accessToken.isNullOrEmpty()) {
                Log.d(TAG, "⚡ 快速启动检查：用户未认证或缺少令牌")
                return false
            }

            // 检查令牌是否过期（如果有过期时间信息）
            if (tokenExpiresAt != null) {
                val currentTime = System.currentTimeMillis()
                val bufferTime = 5 * 60 * 1000 // 5分钟缓冲时间

                if (currentTime >= (tokenExpiresAt - bufferTime)) {
                    Log.d(TAG, "⚡ 快速启动检查：令牌即将过期或已过期")
                    return false
                }
            }

            Log.i(TAG, "⚡ 快速启动检查：令牌状态良好，可以快速启动")

            // 加载用户信息到应用状态
            try {
                Log.d(TAG, "⚡ 快速启动检查：开始读取用户信息...")

                // 🔑 关键修复：一次性读取所有数据，避免DataStore并发访问问题
                val allData = userPreferences.getAllAuthData()

                val userId = allData.userId
                val userEmail = allData.userEmail
                val userName = allData.userName
                val storedAccessToken = allData.accessToken ?: accessToken // 使用已读取的accessToken作为fallback
                val storedIdToken = allData.idToken
                val storedTokenExpiresAt = allData.tokenExpiresAt ?: tokenExpiresAt // 使用已读取的tokenExpiresAt作为fallback

                Log.d(TAG, "  - 读取userId: $userId")
                Log.d(TAG, "  - 读取userEmail: $userEmail")
                Log.d(TAG, "  - 读取userName: $userName")
                Log.d(TAG, "  - 读取accessToken: ${storedAccessToken?.take(20)}...")
                Log.d(TAG, "  - 读取idToken: ${if (storedIdToken != null) "存在" else "null"}")
                Log.d(TAG, "  - 读取tokenExpiresAt: $storedTokenExpiresAt")

                Log.d(TAG, "⚡ 快速启动检查：用户信息和令牌读取完成")
                Log.d(TAG, "  - userId: $userId")
                Log.d(TAG, "  - userEmail: $userEmail")
                Log.d(TAG, "  - userName: $userName")
                Log.d(TAG, "  - accessToken: ${storedAccessToken?.take(20)}...")
                Log.d(TAG, "  - idToken: ${if (storedIdToken != null) "存在" else "null"}")
                Log.d(TAG, "  - tokenExpiresAt: $storedTokenExpiresAt")

                if (!userId.isNullOrEmpty()) {
                    // 创建User对象
                    val user = User(
                        userId = userId,
                        username = userName ?: "",
                        email = userEmail ?: "",
                        phone = "",
                        nickname = userName ?: "",
                        status = 1,
                        createdAt = "",
                        updatedAt = ""
                    )

                    // 更新认证状态，包含用户信息和令牌信息
                    updateAuthState(
                        isAuthenticated = true,
                        user = user,
                        accessToken = storedAccessToken,
                        idToken = storedIdToken,
                        tokenExpiresAt = storedTokenExpiresAt,
                        isLoading = false
                    )
                    Log.i(TAG, "⚡ 快速启动检查：用户信息和令牌已加载到应用状态")
                } else {
                    Log.w(TAG, "⚡ 快速启动检查：用户ID为空，仅更新认证状态和令牌")
                    updateAuthState(
                        isAuthenticated = true,
                        accessToken = storedAccessToken,
                        idToken = storedIdToken,
                        tokenExpiresAt = storedTokenExpiresAt,
                        isLoading = false
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "⚡ 快速启动检查：加载用户信息失败: ${e.message}", e)
                // 即使用户信息加载失败，仍然可以继续（令牌有效）
                updateAuthState(isAuthenticated = true, isLoading = false)
            }

            return true
        } catch (e: Exception) {
            Log.e(TAG, "⚡ 快速启动检查失败: ${e.message}")
            return false
        }
    }

    /**
     * 验证所有认证数据是否正确保存
     */
    private suspend fun verifyAuthDataSaved(): Boolean {
        return try {
            Log.d(TAG, "🔍 验证认证数据保存状态...")

            val isAuthenticated = userPreferences.isAuthenticated().first()
            val accessToken = userPreferences.getAccessToken().first()
            val refreshToken = userPreferences.getRefreshToken().first()
            val userId = userPreferences.getUserId().first()
            val tokenExpiresAt = userPreferences.getTokenExpiresAt().first()

            Log.d(TAG, "🔍 验证结果:")
            Log.d(TAG, "  - isAuthenticated: $isAuthenticated")
            Log.d(TAG, "  - accessToken存在: ${!accessToken.isNullOrEmpty()}")
            Log.d(TAG, "  - refreshToken存在: ${!refreshToken.isNullOrEmpty()}")
            Log.d(TAG, "  - userId存在: ${!userId.isNullOrEmpty()}")
            Log.d(TAG, "  - tokenExpiresAt存在: ${tokenExpiresAt != null}")

            val isValid = isAuthenticated &&
                         !accessToken.isNullOrEmpty() &&
                         !userId.isNullOrEmpty()

            if (isValid) {
                Log.i(TAG, "✅ 认证数据验证通过")
            } else {
                Log.e(TAG, "❌ 认证数据验证失败")
            }

            isValid
        } catch (e: Exception) {
            Log.e(TAG, "❌ 验证认证数据时发生异常: ${e.message}", e)
            false
        }
    }



    /**
     * 原生Logto登录方法 - 使用Logto SDK内部令牌管理
     */
    suspend fun signInNative(activity: android.app.Activity): Result<Unit> {
        return try {
            Log.i(TAG, "🚀 开始原生Logto登录（使用SDK内部令牌管理）")
            updateAuthState(isLoading = true, error = null)

            // 验证配置
            if (!com.cabycare.android.debug.LogtoDebugHelper.checkConfiguration()) {
                val error = "Logto配置验证失败"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                return Result.failure(Exception(error))
            }

            Log.i(TAG, "📱 调用Logto SDK登录")
            logtoManager.signIn(activity)

            Log.i(TAG, "🔑 获取有效访问令牌（SDK会自动处理刷新）")
            // 使用getAccessToken获取有效的令牌（SDK会自动处理刷新）
            val accessToken = logtoManager.getAccessToken()
            val idToken = logtoManager.getUserInfo()?.get("sub") as? String

            Log.d(TAG, "访问令牌: ${if (accessToken != null) "已获取" else "未获取"}")
            Log.d(TAG, "ID令牌: ${if (idToken != null) "已获取" else "未获取"}")
            Log.d(TAG, "刷新令牌: 由SDK内部管理")

            if (accessToken != null) {
                Log.i(TAG, "💾 保存令牌到本地存储")
                // 保存令牌到本地存储（refresh token由SDK管理，无需手动保存）
                userPreferences.saveAccessToken(accessToken)
                idToken?.let { userPreferences.saveIdToken(it) }

                Log.i(TAG, "👤 获取Logto用户信息")
                // 获取Logto用户信息
                val userInfo = logtoManager.getUserInfo()
                // 定义finalUser变量在更外层作用域
                var finalUser: User? = null

                userInfo?.let { info ->
                    Log.d(TAG, "Logto用户信息: $info")
                    val logtoId = info["sub"] as? String // Logto认证服务的用户ID
                    val email = info["email"] as? String
                    val name = info["name"] as? String

                    logtoId?.let {
                        userPreferences.saveLogtoId(it)
                        Log.i(TAG, "✅ 保存Logto ID: $it")

                        // 🔑 关键修复：调用后端API获取user_id
                        Log.i(TAG, "🔄 调用后端用户信息API获取user_id...")

                        try {
                            // 调用 /api/user/info 获取用户信息，包含正确的user_id
                            val userInfoResult = getUserProfile()
                            when (userInfoResult) {
                                is NetworkResult.Success -> {
                                    val userInfo = userInfoResult.data
                                    Log.i(TAG, "✅ 获取到后端user_id: ${userInfo.userId}")

                                    // 保存正确的user_id和用户信息
                                    userPreferences.saveUserInfo(userInfo.userId, userInfo.email, userInfo.nickname ?: userInfo.username)

                                    // 创建User对象用于AuthState
                                    finalUser = User(
                                        userId = userInfo.userId,
                                        username = userInfo.username,
                                        email = userInfo.email,
                                        phone = "",
                                        nickname = userInfo.nickname ?: "",
                                        status = 1,
                                        createdAt = "",
                                        updatedAt = ""
                                    )
                                    Log.i(TAG, "✅ 创建User对象用于AuthState: ${finalUser?.userId}")
                                }
                                is NetworkResult.Error -> {
                                    Log.e(TAG, "❌ 获取用户信息失败: ${userInfoResult.exception.message}")
                                    // 如果获取用户信息失败，我们暂时使用logto_id作为fallback
                                    userPreferences.saveUserId(logtoId)
                                    userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")

                                    // 创建fallback User对象
                                    finalUser = User(
                                        userId = logtoId,
                                        username = email ?: "",
                                        email = email ?: "",
                                        phone = "",
                                        nickname = name ?: "",
                                        status = 1,
                                        createdAt = "",
                                        updatedAt = ""
                                    )
                                    Log.i(TAG, "✅ 创建fallback User对象: ${finalUser?.userId}")
                                }
                                else -> {
                                    Log.w(TAG, "⚠️ 获取用户信息返回未知结果")
                                    userPreferences.saveUserId(logtoId)
                                    userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")

                                    // 创建fallback User对象
                                    finalUser = User(
                                        userId = logtoId,
                                        username = email ?: "",
                                        email = email ?: "",
                                        phone = "",
                                        nickname = name ?: "",
                                        status = 1,
                                        createdAt = "",
                                        updatedAt = ""
                                    )
                                    Log.i(TAG, "✅ 创建fallback User对象: ${finalUser?.userId}")
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "❌ 获取用户信息异常: ${e.message}")
                            // 异常情况下使用logto_id作为fallback
                            userPreferences.saveUserId(logtoId)
                            userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")

                            // 创建fallback User对象
                            finalUser = User(
                                userId = logtoId,
                                username = email ?: "",
                                email = email ?: "",
                                phone = "",
                                nickname = name ?: "",
                                status = 1,
                                createdAt = "",
                                updatedAt = ""
                            )
                            Log.i(TAG, "✅ 创建异常fallback User对象: ${finalUser?.userId}")
                        }
                    }
                }

                // 更新AuthState，包含用户信息和令牌信息
                updateAuthState(
                    isAuthenticated = true,
                    user = finalUser,
                    accessToken = accessToken,
                    idToken = idToken,
                    isLoading = false
                )
                Log.i(TAG, "✅ 原生登录成功，用户信息和令牌已更新到AuthState: ${finalUser?.userId}")
                Result.success(Unit)
            } else {
                val error = "无法获取访问令牌"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                Result.failure(Exception(error))
            }
        } catch (e: Exception) {
            val errorMsg = "原生登录失败: ${e.message}"
            Log.e(TAG, "❌ $errorMsg", e)
            updateAuthState(isAuthenticated = false, isLoading = false, error = e.message)
            Result.failure(e)
        }
    }



    /**
     * 登出方法
     */
    suspend fun signOut(): Result<Unit> {
        return try {
            updateAuthState(isLoading = true)

            // 使用Logto SDK登出
            logtoManager.signOut()

            // 清除本地存储的认证信息
            clearAuthCredentials()

            Log.i(TAG, "✅ 登出成功")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 登出失败", e)
            // 即使登出失败，也清除本地认证信息
            clearAuthCredentials()
            Result.failure(e)
        }
    }
}


