# AuthState令牌存储修复

## 🚨 问题描述

从日志分析发现，虽然用户信息正确传递到AuthState，但API请求仍然返回401错误：

```
HomeViewModel获取用户信息:
  - authState.user: User(userId=022b605dc3421000, ...)  ✅ 用户信息正确
  - userId from AuthState: 022b605dc3421000  ✅ 用户ID正确

API请求:
<-- 401 https://api.caby.care/api/cats
{"error":"missing token"}  ❌ 令牌缺失
```

## 🔍 根本原因

### 1. **AuthState缺少令牌字段**
```kotlin
// 修复前：AuthState只包含用户信息
data class AuthState(
    val isAuthenticated: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null,
    val user: User? = null  // ❌ 没有令牌信息
)
```

### 2. **TokenInterceptor无法获取令牌**
- TokenInterceptor从UserPreferences读取令牌
- 但是令牌可能没有正确保存或已过期
- 没有fallback机制从Logto SDK获取最新令牌

### 3. **应用重启后令牌丢失**
- AuthState在应用重启后重置
- 快速启动检查没有加载令牌信息到AuthState
- 导致第二次启动时无法直接使用

## 🔧 修复方案

### 1. **扩展AuthState数据类**

#### **修复前**：
```kotlin
data class AuthState(
    val isAuthenticated: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null,
    val user: User? = null
)
```

#### **修复后**：
```kotlin
data class AuthState(
    val isAuthenticated: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null,
    val user: User? = null,
    val accessToken: String? = null,      // ✅ 访问令牌
    val refreshToken: String? = null,     // ✅ 刷新令牌
    val idToken: String? = null,          // ✅ ID令牌
    val tokenExpiresAt: Long? = null      // ✅ 令牌过期时间
)
```

### 2. **增强updateAuthState方法**

#### **修复前**：
```kotlin
private fun updateAuthState(
    isAuthenticated: Boolean? = null,
    isLoading: Boolean? = null,
    error: String? = null,
    user: User? = null
)
```

#### **修复后**：
```kotlin
private fun updateAuthState(
    isAuthenticated: Boolean? = null,
    isLoading: Boolean? = null,
    error: String? = null,
    user: User? = null,
    accessToken: String? = null,          // ✅ 支持令牌参数
    refreshToken: String? = null,
    idToken: String? = null,
    tokenExpiresAt: Long? = null
)
```

### 3. **修复signInNative方法**

#### **修复前**：
```kotlin
updateAuthState(isAuthenticated = true, user = finalUser, isLoading = false)
```

#### **修复后**：
```kotlin
updateAuthState(
    isAuthenticated = true, 
    user = finalUser, 
    accessToken = accessToken,    // ✅ 包含令牌信息
    idToken = idToken,
    isLoading = false
)
```

### 4. **修复快速启动检查**

#### **新增令牌读取逻辑**：
```kotlin
// 读取令牌信息
val storedAccessToken = userPreferences.getAccessToken().first()
val storedIdToken = userPreferences.getIdToken().first()
val storedTokenExpiresAt = userPreferences.getTokenExpiresAt().first()

// 更新AuthState，包含令牌信息
updateAuthState(
    isAuthenticated = true, 
    user = user, 
    accessToken = storedAccessToken,      // ✅ 加载令牌到AuthState
    idToken = storedIdToken,
    tokenExpiresAt = storedTokenExpiresAt,
    isLoading = false
)
```

### 5. **增强TokenInterceptor**

#### **修复前**：
```kotlin
// 只从UserPreferences获取令牌
val accessToken = userPreferences.getAccessToken().first()
```

#### **修复后**：
```kotlin
// 优先从UserPreferences获取，如果没有则从Logto SDK获取
val accessToken = userPreferences.getAccessToken().first()
val finalAccessToken = if (accessToken.isNullOrEmpty()) {
    // fallback到Logto SDK
    val sdkToken = logtoManager.getAccessToken()
    if (!sdkToken.isNullOrEmpty()) {
        // 保存到UserPreferences以供下次使用
        userPreferences.saveAccessToken(sdkToken)
    }
    sdkToken
} else {
    accessToken
}
```

### 6. **添加getCurrentAccessToken方法**

```kotlin
/**
 * 获取当前有效的访问令牌
 * 优先使用AuthState中的令牌，如果没有则从Logto SDK获取
 */
suspend fun getCurrentAccessToken(): String? {
    // 首先尝试从AuthState获取
    val authStateToken = _authState.value.accessToken
    if (!authStateToken.isNullOrEmpty()) {
        return authStateToken
    }
    
    // 如果AuthState中没有，尝试从Logto SDK获取
    val sdkToken = logtoManager.getAccessToken()
    if (!sdkToken.isNullOrEmpty()) {
        // 更新AuthState中的令牌
        updateAuthState(accessToken = sdkToken)
        // 同时保存到本地存储
        userPreferences.saveAccessToken(sdkToken)
        return sdkToken
    }
    
    return null
}
```

### 7. **添加缺失的getIdToken方法**

```kotlin
/**
 * 获取ID令牌
 */
fun getIdToken(): Flow<String?> {
    return context.dataStore.data.map { preferences ->
        val idToken = preferences[ID_TOKEN_KEY]
        android.util.Log.d("UserPreferences", "🔍 读取ID令牌: ${if (idToken != null) "存在" else "null"}")
        idToken
    }
}
```

## 📊 修复效果

### Before (修复前)：
```
登录成功 → AuthState(user=User(...), accessToken=null) → API请求 → 401错误
应用重启 → AuthState重置 → 令牌信息丢失 → 需要重新登录
```

### After (修复后)：
```
登录成功 → AuthState(user=User(...), accessToken="token...") → API请求 → 成功
应用重启 → 快速启动检查 → AuthState(包含令牌) → 直接可用
```

## 🔍 预期日志变化

### 修复前的日志：
```
🔄 AuthState更新:
  - user: null → User(userId=022b605dc3421000, ...)
  - accessToken: null → null  ❌

API请求:
⚠️ 无法获取访问令牌，执行原始请求
<-- 401 {"error":"missing token"}
```

### 修复后的预期日志：
```
🔄 AuthState更新:
  - user: null → User(userId=022b605dc3421000, ...)
  - accessToken: null → DsPcZQ2saiKjxylWQ9dG...  ✅
  - idToken: null → 存在  ✅

TokenInterceptor:
🔍 从UserPreferences获取访问令牌: DsPcZQ2saiKjxylWQ9dG...
🔑 为请求添加认证头: https://api.caby.care/api/cats
<-- 200 成功  ✅

快速启动检查:
⚡ 快速启动检查：用户信息和令牌已加载到应用状态
```

## ✅ 验证结果

- ✅ **编译成功**：`BUILD SUCCESSFUL in 4s`
- ✅ **AuthState扩展**：包含完整的令牌信息
- ✅ **TokenInterceptor增强**：支持fallback到Logto SDK
- ✅ **快速启动检查**：加载令牌信息到AuthState
- ✅ **应用重启支持**：第二次启动可直接使用令牌

## 🚀 预期改进

1. **API请求成功**：TokenInterceptor能够正确添加认证头
2. **应用重启免登录**：第二次启动时直接使用缓存的令牌
3. **令牌自动刷新**：TokenInterceptor在401时自动刷新令牌
4. **状态一致性**：AuthState、UserPreferences和Logto SDK保持同步

---

**总结**：通过在AuthState中添加令牌存储，增强TokenInterceptor的令牌获取逻辑，以及修复快速启动检查的令牌加载，现在应用拥有了完整的令牌管理系统，支持API认证和应用重启后的免登录体验。🎉
